<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToDo Management</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            width: 100%;
            box-sizing: border-box;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        header h1 {
            color: white;
            margin: 0;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .card-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 {
            margin: 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #ddd;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        .placeholder-content {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-style: italic;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .data-table th {
            background-color: #2c3e50;
            color: white;
            text-align: left;
            padding: 12px;
            border: 1px solid #ddd;
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        .data-table th:hover {
            background-color: #34495e;
        }
        .data-table th.sortable::after {
            content: ' ↕';
            opacity: 0.5;
            margin-left: 5px;
        }
        .data-table th.sort-asc::after {
            content: ' ↑';
            opacity: 1;
        }
        .data-table th.sort-desc::after {
            content: ' ↓';
            opacity: 1;
        }
        .data-table td {
            padding: 10px 12px;
            border: 1px solid #ddd;
            text-align: right;
        }
        .data-table td:first-child,
        .data-table td:nth-child(2) {
            text-align: left;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .data-table tr:hover {
            background-color: #e9ecef;
        }
        .action-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0;
        }
        .action-btn:hover {
            background-color: #c0392b;
        }
        .loading-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .refresh-btn {
            background-color: #27ae60;
            margin-bottom: 15px;
        }
        .refresh-btn:hover {
            background-color: #229954;
        }
        .editable-field {
            background: none;
            border: 1px solid transparent;
            padding: 4px 8px;
            width: 100%;
            text-align: right;
            font-family: inherit;
            font-size: inherit;
        }
        .editable-field:hover {
            border-color: #3498db;
            background-color: #f8f9fa;
        }
        .editable-field:focus {
            outline: none;
            border-color: #3498db;
            background-color: white;
            box-shadow: 0 0 3px rgba(52, 152, 219, 0.3);
        }
        .verify-btn {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin: 0;
        }
        .verify-btn:hover {
            background-color: #229954;
        }
        .verify-btn:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }

        /* SDASINS table specific styles */
        #sdasinsTable {
            font-size: 12px;
            width: 100%;
        }

        #sdasinsTable th,
        #sdasinsTable td {
            padding: 6px 8px;
            font-size: 12px;
            vertical-align: top;
        }

        /* Sticky header styles */
        #sdasinsTable thead th {
            position: sticky;
            top: 0;
            background-color: #343a40;
            color: white;
            border-bottom: 2px solid #dee2e6;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            font-weight: bold;
        }

        #sdasinsTable thead th.sortable {
            cursor: pointer;
        }

        #sdasinsTable thead th.sortable:hover {
            background-color: #495057;
        }

        /* Parent ASIN filter styles */
        .parent-asin-filter:hover {
            background-color: #e9ecef !important;
            color: #0056b3 !important;
        }

        /* Parent ASIN edit button styles */
        .asin-cell:hover button {
            opacity: 1 !important;
        }

        #sdasinsTable .notes-cell {
            width: 300px;
            max-width: 300px;
            min-width: 300px;
        }

        #sdasinsTable .raw-notes-cell {
            width: 250px;
            max-width: 250px;
            min-width: 250px;
        }

        #sdasinsTable .asin-cell {
            width: 100px;
            max-width: 100px;
            min-width: 100px;
        }

        #sdasinsTable .weight-cell {
            width: 60px;
            max-width: 60px;
            min-width: 60px;
        }

        #sdasinsTable .color-cell {
            width: 110px;
            max-width: 110px;
            min-width: 110px;
        }

        #sdasinsTable .rank-cell {
            width: 80px;
            max-width: 80px;
            min-width: 80px;
        }

        #sdasinsTable .date-cell {
            width: 100px;
            max-width: 100px;
            min-width: 100px;
            font-size: 11px;
        }

        #sdasinsTable .notes-input,
        #sdasinsTable .raw-notes-input {
            width: 100%;
            min-height: 40px;
            resize: vertical;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 11px;
            line-height: 1.3;
            padding: 4px 6px;
        }

        #sdasinsTable .compact-input {
            font-size: 11px;
            padding: 3px 5px;
        }

        #sdasinsTable .weight-input {
            width: 50px;
            font-size: 11px;
            padding: 2px 3px;
        }

        #sdasinsTable .color-input {
            width: 100px;
            font-size: 11px;
            padding: 2px 3px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }

        #sdasinsTable .asin-link {
            color: #007bff;
            text-decoration: none;
            font-size: 11px;
            word-break: break-all;
        }

        #sdasinsTable .asin-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <header>
        <h1>ToDo Management Dashboard</h1>
        <p>Manage tasks and items across different data types</p>
    </header>



    <div class="tabs">
        <div class="tab active" data-tab="plastic">Plastic</div>
        <div class="tab" data-tab="mold">Mold</div>
        <div class="tab" data-tab="mps">MPS</div>
        <div class="tab" data-tab="osl">OSL</div>
        <div class="tab" data-tab="disc">Disc</div>
        <div class="tab" data-tab="mps-review">MPS Review</div>
        <div class="tab" data-tab="sdasins">SDASINS</div>
    </div>

    <div id="plastic" class="tab-content active">
        <div class="card">
            <div class="card-header">
                <h2>Plastic Pricing Review</h2>
                <button id="refreshPlasticPricingBtn" class="refresh-btn">Refresh Data</button>
            </div>
            <div>
                <p>Review and update plastic pricing information. Edit price fields directly in the table and verify cost/price data.</p>

                <!-- Search bar -->
                <div style="margin-bottom: 15px;">
                    <input type="text" id="plasticSearchInput" placeholder="Search plastics..." style="padding: 8px; width: 300px; border: 1px solid #ddd; border-radius: 4px;">
                </div>

                <div id="plasticPricingTableContainer">
                    <table class="data-table" id="plasticPricingTable">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="id" data-type="number">ID</th>
                                <th class="sortable" data-column="plastic" data-type="text">Plastic</th>
                                <th class="sortable" data-column="discs_in_stock_and_uploaded" data-type="number">Discs in Stock & Uploaded</th>
                                <th class="sortable" data-column="val_order_cost" data-type="number">Order Cost</th>
                                <th class="sortable" data-column="val_map_price" data-type="number">MAP Price</th>
                                <th class="sortable" data-column="val_retail_price" data-type="number">Retail Price</th>
                                <th class="sortable" data-column="val_msrp" data-type="number">MSRP</th>
                                <th class="sortable" data-column="val_max_amazon_price" data-type="number">Max Amazon Price</th>
                                <th class="sortable" data-column="price_cost_verified_at" data-type="date">Price Verified At</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="plasticPricingTableBody">
                            <tr>
                                <td colspan="10" class="loading-message">Click "Refresh Data" to load plastic pricing data...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="mold" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Mold ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Mold Management</h3>
                <p>This section will contain mold-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mps" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>MPS ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>MPS Management</h3>
                <p>This section will contain MPS-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="osl" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>OSL ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>OSL Management</h3>
                <p>This section will contain OSL-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="disc" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Disc ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Disc Management</h3>
                <p>This section will contain disc-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mps-review" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>MPS Review - Active MPS with No Stock</h2>
            </div>
            <div>
                <p>Review active MPS records that currently have no discs in stock. These may be candidates for marking as inactive.</p>
                <button id="refreshMpsReviewBtn" class="refresh-btn">Refresh Data</button>

                <div id="mpsReviewTableContainer">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="id" data-type="number">MPS ID</th>
                                <th class="sortable" data-column="g_code" data-type="text">G Code</th>
                                <th class="sortable" data-column="sold_date_last" data-type="date">Last Sold Date</th>
                                <th class="sortable" data-column="received_date_last" data-type="date">Last Received Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="mpsReviewTableBody">
                            <tr>
                                <td colspan="5" class="loading-message">Click "Refresh Data" to load MPS records...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="sdasins" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>SDASINS Management</h2>
                <button id="refreshSdasinsBtn" class="refresh-btn">Refresh Data</button>
            </div>
            <div>
                <p>Review and edit SDASINS records. Edit fields directly in the table.</p>

                <!-- Search bar -->
                <div style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <input type="text" id="sdasinsSearchInput" placeholder="Search SDASINS..." style="padding: 8px; width: 300px; border: 1px solid #ddd; border-radius: 4px;">
                        <div style="font-size: 11px; color: #6c757d; margin-top: 3px;">
                            💡 Tip: Hold Ctrl/Cmd while clicking column headers for cascading sorts
                        </div>
                    </div>
                    <div id="sdasinsRecordCount" style="color: #6c757d; font-size: 14px; font-style: italic;"></div>
                </div>

                <!-- Filters -->
                <div style="margin-bottom: 15px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border: 1px solid #dee2e6;">
                    <h4 style="margin-top: 0; margin-bottom: 10px; color: #495057;">Filters</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: 15px; align-items: center; margin-bottom: 10px;">
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="checkbox" id="filterRankBelow" style="margin: 0;">
                            <span>Rank below</span>
                            <input type="number" id="filterRankValue" value="100000" style="width: 80px; padding: 2px 4px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px;">
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="checkbox" id="filterIdAbove" style="margin: 0;">
                            <span>ID above</span>
                            <input type="number" id="filterIdValue" value="40000" style="width: 70px; padding: 2px 4px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px;">
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="checkbox" id="filterNotesNotContain" style="margin: 0;">
                            <span>Notes does not contain</span>
                            <input type="text" id="filterNotesValue" value="XXXX" style="width: 60px; padding: 2px 4px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px;">
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="checkbox" id="filterNotesNotNull" style="margin: 0;">
                            <span>Notes not null</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 5px;">
                            <input type="checkbox" id="filterColorIsNull" style="margin: 0;">
                            <span>Color is null</span>
                        </label>
                        <button id="clearAllFilters" style="background-color: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 12px;">Clear All</button>
                    </div>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <button id="refreshWithFilters" style="background-color: #28a745; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: bold;">Apply Filters & Refresh Data</button>
                        <span style="color: #6c757d; font-size: 12px; font-style: italic;">Adjust filters above, then click to load filtered data from server</span>
                    </div>
                </div>

                <div id="sdasinsTableContainer" style="width: 100%; overflow-x: auto;">
                    <table class="data-table" id="sdasinsTable">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="id" data-type="number">ID</th>
                                <th class="sortable" data-column="parent_asin" data-type="text">Parent ASIN</th>
                                <th class="sortable" data-column="asin" data-type="text">ASIN</th>
                                <th class="sortable" data-column="notes" data-type="text">Notes</th>
                                <th class="sortable" data-column="raw_notes" data-type="text">Raw Notes</th>
                                <th class="sortable" data-column="parsed_brand" data-type="text">Parsed Brand</th>
                                <th class="sortable" data-column="parsed_mold" data-type="text">Parsed Mold</th>
                                <th class="sortable" data-column="parsed_plastic" data-type="text">Parsed Plastic</th>
                                <th class="sortable" data-column="parsed_stamp" data-type="text">Parsed Stamp</th>
                                <th class="sortable" data-column="parsed_min_weight" data-type="number">Min Wt</th>
                                <th class="sortable" data-column="parsed_max_weight" data-type="number">Max Wt</th>
                                <th class="sortable" data-column="color_id" data-type="number">Color</th>
                                <th class="sortable" data-column="so_rank_30day_avg" data-type="number">Rank</th>
                                <th class="sortable" data-column="so_rank_30day_avg_date" data-type="date">Rank Date</th>
                                <th class="sortable" data-column="fbm_uploaded_at" data-type="date">FBM Uploaded</th>
                            </tr>
                        </thead>
                        <tbody id="sdasinsTableBody">
                            <tr>
                                <td colspan="15" class="loading-message">Click "Refresh Data" to load SDASINS data...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // MPS Review functionality
        document.getElementById('refreshMpsReviewBtn').addEventListener('click', function() {
            loadMpsReviewData();
        });

        // Plastic Pricing functionality
        document.getElementById('refreshPlasticPricingBtn').addEventListener('click', function() {
            loadPlasticPricingData();
        });

        // SDASINS functionality
        document.getElementById('refreshSdasinsBtn').addEventListener('click', function() {
            loadSdasinsData();
        });

        // SDASINS filter functionality
        document.getElementById('refreshWithFilters').addEventListener('click', function() {
            loadSdasinsDataWithFilters();
        });

        document.getElementById('clearAllFilters').addEventListener('click', function() {
            document.getElementById('filterRankBelow').checked = false;
            document.getElementById('filterRankValue').value = '100000';
            document.getElementById('filterIdAbove').checked = false;
            document.getElementById('filterIdValue').value = '40000';
            document.getElementById('filterNotesNotContain').checked = false;
            document.getElementById('filterNotesValue').value = 'XXXX';
            document.getElementById('filterNotesNotNull').checked = false;
            document.getElementById('filterColorIsNull').checked = false;

            // Clear parent ASIN filter and reload with no filters
            if (currentParentAsinFilter) {
                currentParentAsinFilter = null;
                updateParentAsinFilterIndicator();
                loadSdasinsData(); // Load all data with no filters
            }
        });

        // Global variable to store current data for sorting
        let currentMpsData = [];
        let currentSortColumn = 'id';
        let currentSortDirection = 'asc';

        // Global variables for plastic pricing
        let currentPlasticData = [];
        let currentPlasticSortColumn = 'id';
        let currentPlasticSortDirection = 'asc';

        // Global variables for SDASINS
        let currentSdasinsData = [];
        let currentSdasinsSortColumn = 'id';
        let currentSdasinsSortDirection = 'asc';
        let sdasinsSortCriteria = []; // Array to store multiple sort criteria for cascading
        let colorOptions = []; // Array to store color options for dropdown
        let currentParentAsinFilter = null; // Track current parent ASIN filter

        function loadMpsReviewData() {
            const tableBody = document.getElementById('mpsReviewTableBody');
            tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Loading MPS review data...</td></tr>';

            // Call API to get MPS review data
            fetch('/api/mps-review')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        currentMpsData = data.records;
                        sortAndDisplayData();
                        setupSortingEventListeners();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        function displayMpsReviewData(records) {
            const tableBody = document.getElementById('mpsReviewTableBody');

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">No active MPS records with zero stock found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';

            records.forEach(record => {
                const row = document.createElement('tr');

                // MPS ID
                const idCell = document.createElement('td');
                idCell.textContent = record.id;
                row.appendChild(idCell);

                // G Code
                const codeCell = document.createElement('td');
                codeCell.textContent = record.g_code || '';
                row.appendChild(codeCell);

                // Last Sold Date
                const soldDateCell = document.createElement('td');
                soldDateCell.textContent = record.sold_date_last ? new Date(record.sold_date_last).toLocaleDateString() : '';
                row.appendChild(soldDateCell);

                // Last Received Date
                const receivedDateCell = document.createElement('td');
                receivedDateCell.textContent = record.received_date_last ? new Date(record.received_date_last).toLocaleDateString() : '';
                row.appendChild(receivedDateCell);

                // Action button
                const actionCell = document.createElement('td');
                const actionBtn = document.createElement('button');
                actionBtn.textContent = 'Mark Inactive';
                actionBtn.className = 'action-btn';
                actionBtn.onclick = () => markMpsInactive(record.id);
                actionCell.appendChild(actionBtn);
                row.appendChild(actionCell);

                tableBody.appendChild(row);
            });
        }

        function markMpsInactive(mpsId) {
            // Call API to mark MPS as inactive
            fetch('/api/mps-mark-inactive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mpsId: mpsId }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the record from current data and refresh display
                    currentMpsData = currentMpsData.filter(record => record.id !== mpsId);
                    sortAndDisplayData();
                } else {
                    console.error('Error marking MPS as inactive:', data.error || 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Error marking MPS as inactive:', error.message);
            });
        }

        function setupSortingEventListeners() {
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    const type = this.getAttribute('data-type');

                    // Toggle sort direction if clicking the same column
                    if (currentSortColumn === column) {
                        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSortColumn = column;
                        currentSortDirection = 'asc';
                    }

                    sortAndDisplayData();
                    updateSortIndicators();
                });
            });
        }

        function sortAndDisplayData() {
            const sortedData = [...currentMpsData].sort((a, b) => {
                let aVal = a[currentSortColumn];
                let bVal = b[currentSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentSortColumn === 'id') {
                    aVal = parseInt(aVal) || 0;
                    bVal = parseInt(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayMpsReviewData(sortedData);
        }

        function updateSortIndicators() {
            // Remove all sort classes
            document.querySelectorAll('.sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Add sort class to current column
            const currentHeader = document.querySelector(`[data-column="${currentSortColumn}"]`);
            if (currentHeader) {
                currentHeader.classList.add(currentSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        }

        // Plastic Pricing Functions
        function loadPlasticPricingData() {
            const tableBody = document.getElementById('plasticPricingTableBody');
            tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Loading plastic pricing data...</td></tr>';

            // Call API to get plastic pricing data
            fetch('/api/plastic-pricing')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        currentPlasticData = data.records;
                        sortAndDisplayPlasticData();
                        setupPlasticSortingEventListeners();
                        setupPlasticSearchFilter();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        function displayPlasticPricingData(records) {
            const tableBody = document.getElementById('plasticPricingTableBody');

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">No plastic records found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';

            records.forEach(record => {
                const row = document.createElement('tr');

                // ID
                const idCell = document.createElement('td');
                idCell.textContent = record.id;
                row.appendChild(idCell);

                // Plastic name
                const plasticCell = document.createElement('td');
                plasticCell.textContent = record.plastic || '';
                row.appendChild(plasticCell);

                // Discs in stock and uploaded
                const stockCell = document.createElement('td');
                stockCell.textContent = record.discs_in_stock_and_uploaded || 0;
                stockCell.style.textAlign = 'right';
                row.appendChild(stockCell);

                // Editable price fields
                const priceFields = ['val_order_cost', 'val_map_price', 'val_retail_price', 'val_msrp', 'val_max_amazon_price'];
                priceFields.forEach(field => {
                    const cell = document.createElement('td');
                    const input = document.createElement('input');
                    input.type = 'number';
                    input.step = '0.01';
                    input.className = 'editable-field';
                    input.value = record[field] || '';
                    input.dataset.field = field;
                    input.dataset.id = record.id;
                    input.addEventListener('blur', updatePlasticField);
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            this.blur();
                        }
                    });
                    cell.appendChild(input);
                    row.appendChild(cell);
                });

                // Price verified at
                const verifiedCell = document.createElement('td');
                verifiedCell.textContent = record.price_cost_verified_at ? new Date(record.price_cost_verified_at).toLocaleDateString() : '';
                row.appendChild(verifiedCell);

                // Action button
                const actionCell = document.createElement('td');
                const verifyBtn = document.createElement('button');
                verifyBtn.textContent = 'Verify Cost/Price';
                verifyBtn.className = 'verify-btn';
                verifyBtn.onclick = () => verifyPlasticCostPrice(record.id);
                actionCell.appendChild(verifyBtn);
                row.appendChild(actionCell);

                tableBody.appendChild(row);
            });
        }

        function updatePlasticField(event) {
            const input = event.target;
            const id = input.dataset.id;
            const field = input.dataset.field;
            const value = input.value;

            // Call API to update the field
            fetch('/api/plastic-pricing/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: parseInt(id),
                    field: field,
                    value: value ? parseFloat(value) : null
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the record in current data
                    const record = currentPlasticData.find(r => r.id == id);
                    if (record) {
                        record[field] = value ? parseFloat(value) : null;
                    }
                    // Visual feedback
                    input.style.backgroundColor = '#d4edda';
                    setTimeout(() => {
                        input.style.backgroundColor = '';
                    }, 1000);
                } else {
                    console.error('Error updating plastic field:', data.error || 'Unknown error');
                    // Reset to original value
                    const record = currentPlasticData.find(r => r.id == id);
                    if (record) {
                        input.value = record[field] || '';
                    }
                    // Visual feedback for error
                    input.style.backgroundColor = '#f8d7da';
                    setTimeout(() => {
                        input.style.backgroundColor = '';
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error updating plastic field:', error.message);
                // Reset to original value
                const record = currentPlasticData.find(r => r.id == id);
                if (record) {
                    input.value = record[field] || '';
                }
            });
        }

        function verifyPlasticCostPrice(plasticId) {
            // Call API to verify cost/price
            fetch('/api/plastic-pricing/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: plasticId }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the record in current data
                    const record = currentPlasticData.find(r => r.id == plasticId);
                    if (record) {
                        record.price_cost_verified_at = new Date().toISOString();
                    }
                    // Reapply the current search filter instead of showing all data
                    applyCurrentPlasticSearch();
                } else {
                    console.error('Error verifying plastic cost/price:', data.error || 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Error verifying plastic cost/price:', error.message);
            });
        }

        function setupPlasticSortingEventListeners() {
            document.querySelectorAll('#plasticPricingTable .sortable').forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    const type = this.getAttribute('data-type');

                    // Toggle sort direction if clicking the same column
                    if (currentPlasticSortColumn === column) {
                        currentPlasticSortDirection = currentPlasticSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentPlasticSortColumn = column;
                        currentPlasticSortDirection = 'asc';
                    }

                    sortAndDisplayPlasticData();
                    updatePlasticSortIndicators();
                });
            });
        }

        function sortAndDisplayPlasticData() {
            const sortedData = [...currentPlasticData].sort((a, b) => {
                let aVal = a[currentPlasticSortColumn];
                let bVal = b[currentPlasticSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentPlasticSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentPlasticSortColumn === 'id' || currentPlasticSortColumn.startsWith('val_') || currentPlasticSortColumn === 'discs_in_stock_and_uploaded') {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentPlasticSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentPlasticSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayPlasticPricingData(sortedData);
        }

        function updatePlasticSortIndicators() {
            // Remove all sort classes from plastic table
            document.querySelectorAll('#plasticPricingTable .sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Add sort class to current column
            const currentHeader = document.querySelector(`#plasticPricingTable [data-column="${currentPlasticSortColumn}"]`);
            if (currentHeader) {
                currentHeader.classList.add(currentPlasticSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        }

        function applyCurrentPlasticSearch() {
            const searchInput = document.getElementById('plasticSearchInput');
            const searchTerm = searchInput.value.toLowerCase().trim();

            if (!searchTerm) {
                // If search is empty, show all data sorted
                sortAndDisplayPlasticData();
                return;
            }

            // Split search term into individual words
            const searchWords = searchTerm.split(/\s+/);

            const filteredData = currentPlasticData.filter(record => {
                const plasticName = (record.plastic || '').toLowerCase();
                const plasticId = (record.id || '').toString();

                // Check if ALL search words are found in either plastic name or ID
                return searchWords.every(word =>
                    plasticName.includes(word) || plasticId.includes(word)
                );
            });

            // Sort the filtered data before displaying
            const sortedFilteredData = [...filteredData].sort((a, b) => {
                let aVal = a[currentPlasticSortColumn];
                let bVal = b[currentPlasticSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentPlasticSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentPlasticSortColumn === 'id' || currentPlasticSortColumn.startsWith('val_') || currentPlasticSortColumn === 'discs_in_stock_and_uploaded') {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentPlasticSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentPlasticSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayPlasticPricingData(sortedFilteredData);
        }

        function setupPlasticSearchFilter() {
            const searchInput = document.getElementById('plasticSearchInput');
            searchInput.addEventListener('input', function() {
                applyCurrentPlasticSearch();
            });
        }

        // SDASINS Functions
        function toggleParentAsinFilter(parentAsin) {
            console.log(`toggleParentAsinFilter called with: ${parentAsin}`);
            console.log(`Current filter: ${currentParentAsinFilter}`);

            if (currentParentAsinFilter === parentAsin) {
                // Remove filter if clicking the same parent ASIN - revert to previous state
                currentParentAsinFilter = null;
                console.log('Removed parent ASIN filter - reverting to previous filters');

                // Reload data with original filters
                loadSdasinsDataWithFilters();
            } else {
                // Set new filter - load ALL data to ensure we see every record with this parent ASIN
                currentParentAsinFilter = parentAsin;
                console.log(`Set parent ASIN filter to: ${parentAsin} - loading all data`);

                // Copy parent ASIN to clipboard
                copyToClipboard(parentAsin);

                // Set sort to ASIN for parent ASIN filtering
                currentSdasinsSortColumn = 'asin';
                currentSdasinsSortDirection = 'asc';
                sdasinsSortCriteria = [{ column: 'asin', direction: 'asc', type: 'text' }];

                // Load all data (no server-side filters) to ensure we see every record with this parent ASIN
                loadSdasinsData();
            }
        }

        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                // Use modern clipboard API
                navigator.clipboard.writeText(text).then(() => {
                    console.log(`Copied to clipboard: ${text}`);
                    showClipboardNotification(text);
                }).catch(err => {
                    console.error('Failed to copy to clipboard:', err);
                    fallbackCopyToClipboard(text);
                });
            } else {
                // Fallback for older browsers or non-secure contexts
                fallbackCopyToClipboard(text);
            }
        }

        function fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                console.log(`Copied to clipboard (fallback): ${text}`);
                showClipboardNotification(text);
            } catch (err) {
                console.error('Fallback copy failed:', err);
            }

            document.body.removeChild(textArea);
        }

        function showClipboardNotification(text) {
            // Create a temporary notification
            const notification = document.createElement('div');
            notification.textContent = `Copied: ${text}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 10px 15px;
                border-radius: 4px;
                font-size: 14px;
                z-index: 1000;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            `;

            document.body.appendChild(notification);

            // Remove after 2 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 2000);
        }

        function editParentAsin(cell, record, span) {
            // Create input field
            const input = document.createElement('input');
            input.type = 'text';
            input.value = record.parent_asin || '';
            input.style.cssText = `
                width: 90px;
                font-size: 11px;
                padding: 2px 4px;
                border: 1px solid #007bff;
                border-radius: 3px;
                background: #fff;
            `;

            // Replace the container with input
            cell.innerHTML = '';
            cell.appendChild(input);
            input.focus();
            input.select();

            // Save function
            const saveEdit = () => {
                const newValue = input.value.trim();
                console.log(`Saving parent ASIN edit: ID=${record.id}, old="${record.parent_asin}", new="${newValue}"`);

                // Update via API
                fetch('/api/sdasins/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: parseInt(record.id),
                        field: 'parent_asin',
                        value: newValue || null
                    }),
                })
                .then(response => {
                    console.log('API response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('API response data:', data);
                    if (data.success) {
                        // Update the record in current data
                        record.parent_asin = newValue || null;

                        // Recreate the original cell content
                        recreateParentAsinCell(cell, record);

                        // Visual feedback for success
                        cell.style.backgroundColor = '#d4edda';
                        setTimeout(() => {
                            cell.style.backgroundColor = '';
                        }, 1000);

                        console.log(`Successfully updated parent ASIN for record ${record.id} to: ${newValue}`);
                    } else {
                        console.error('Error updating parent ASIN:', data.error);
                        // Reset to original value and recreate cell
                        recreateParentAsinCell(cell, record);

                        // Visual feedback for error
                        cell.style.backgroundColor = '#f8d7da';
                        setTimeout(() => {
                            cell.style.backgroundColor = '';
                        }, 2000);
                    }
                })
                .catch(error => {
                    console.error('Error updating parent ASIN:', error.message);
                    // Reset to original value and recreate cell
                    recreateParentAsinCell(cell, record);
                });
            };

            // Cancel function
            const cancelEdit = () => {
                recreateParentAsinCell(cell, record);
            };

            // Event listeners
            input.addEventListener('blur', saveEdit);
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveEdit();
                } else if (e.key === 'Escape') {
                    cancelEdit();
                }
            });
        }

        function recreateParentAsinCell(cell, record) {
            // Recreate the original parent ASIN cell content
            cell.innerHTML = '';
            cell.style.position = 'relative';

            const parentAsinContainer = document.createElement('div');
            parentAsinContainer.style.display = 'flex';
            parentAsinContainer.style.alignItems = 'center';
            parentAsinContainer.style.gap = '5px';

            const parentAsinSpan = document.createElement('span');
            parentAsinSpan.textContent = record.parent_asin || '';
            parentAsinSpan.className = 'parent-asin-filter';
            parentAsinSpan.style.color = '#007bff';
            parentAsinSpan.style.cursor = 'pointer';
            parentAsinSpan.style.textDecoration = 'underline';
            parentAsinSpan.style.flex = '1';
            parentAsinSpan.style.minWidth = '0';
            parentAsinSpan.title = 'Click to filter by this parent ASIN';
            parentAsinSpan.addEventListener('click', function(e) {
                e.stopPropagation();
                if (record.parent_asin) {
                    toggleParentAsinFilter(record.parent_asin);
                }
            });

            const editButton = document.createElement('button');
            editButton.textContent = '✏️';
            editButton.style.cssText = `
                background: none;
                border: none;
                cursor: pointer;
                font-size: 12px;
                padding: 2px;
                opacity: 0.6;
                flex-shrink: 0;
            `;
            editButton.title = 'Edit parent ASIN';
            editButton.addEventListener('click', function(e) {
                e.stopPropagation();
                editParentAsin(cell, record, parentAsinSpan);
            });

            parentAsinContainer.appendChild(parentAsinSpan);
            parentAsinContainer.appendChild(editButton);
            cell.appendChild(parentAsinContainer);
        }

        function updateParentAsinFilterIndicator() {
            const recordCountDiv = document.getElementById('sdasinsRecordCount');
            if (currentParentAsinFilter) {
                // Update all parent ASIN spans to show which one is active
                document.querySelectorAll('.parent-asin-filter').forEach(span => {
                    if (span.textContent === currentParentAsinFilter) {
                        span.style.backgroundColor = '#007bff';
                        span.style.color = 'white';
                        span.style.padding = '2px 4px';
                        span.style.borderRadius = '3px';
                        span.title = 'Click to remove parent ASIN filter';
                    } else {
                        span.style.backgroundColor = '';
                        span.style.color = '#007bff';
                        span.style.padding = '';
                        span.style.borderRadius = '';
                        span.title = 'Click to filter by this parent ASIN';
                    }
                });
            } else {
                // Reset all parent ASIN spans
                document.querySelectorAll('.parent-asin-filter').forEach(span => {
                    span.style.backgroundColor = '';
                    span.style.color = '#007bff';
                    span.style.padding = '';
                    span.style.borderRadius = '';
                    span.title = 'Click to filter by this parent ASIN';
                });
            }
        }

        function loadColorOptions() {
            return fetch('/api/colors')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.colors) {
                        colorOptions = data.colors;
                        console.log(`Loaded ${colorOptions.length} color options`);
                        return colorOptions;
                    } else {
                        console.error('Error loading color options:', data.error || 'Unknown error');
                        return [];
                    }
                })
                .catch(error => {
                    console.error('Error loading color options:', error.message);
                    return [];
                });
        }

        function loadSdasinsData() {
            const tableBody = document.getElementById('sdasinsTableBody');
            tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Loading color options and SDASINS data...</td></tr>';

            // Load color options first, then SDASINS data
            loadColorOptions().then(() => {
                // Call API to get SDASINS data without filters
                fetch('/api/sdasins')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        // Process records to flatten color data
                        currentSdasinsData = data.records.map(record => ({
                            ...record,
                            color_name: record.t_colors?.color || ''
                        }));
                        console.log(`Loaded ${currentSdasinsData.length} SDASINS records`);

                        // Apply parent ASIN filter if active, otherwise display all data
                        if (currentParentAsinFilter) {
                            console.log(`Applying parent ASIN filter after data load: ${currentParentAsinFilter}`);
                            applyCurrentSdasinsSearch();
                            updateSdasinsSortIndicators(); // Update sort indicators for ASIN sort
                        } else {
                            displaySdasinsData(currentSdasinsData);
                        }
                        setupSdasinsSortingEventListeners();
                        setupSdasinsSearchFilter();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
            });
        }

        function loadSdasinsDataWithFilters() {
            const tableBody = document.getElementById('sdasinsTableBody');
            tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Loading color options and filtered SDASINS data...</td></tr>';

            // Load color options first if not already loaded
            const colorPromise = colorOptions.length > 0 ? Promise.resolve() : loadColorOptions();

            colorPromise.then(() => {

            // Build filter parameters
            const filterParams = new URLSearchParams();

            if (document.getElementById('filterRankBelow').checked) {
                const rankValue = document.getElementById('filterRankValue').value;
                if (rankValue) {
                    filterParams.append('rankBelow', rankValue);
                }
            }

            if (document.getElementById('filterIdAbove').checked) {
                const idValue = document.getElementById('filterIdValue').value;
                if (idValue) {
                    filterParams.append('idAbove', idValue);
                }
            }

            if (document.getElementById('filterNotesNotContain').checked) {
                const notesValue = document.getElementById('filterNotesValue').value;
                if (notesValue) {
                    filterParams.append('notesNotContain', notesValue);
                }
            }

            if (document.getElementById('filterNotesNotNull').checked) {
                filterParams.append('notesNotNull', 'true');
            }

            if (document.getElementById('filterColorIsNull').checked) {
                filterParams.append('colorIsNull', 'true');
            }

            // Call API with filter parameters
            const url = '/api/sdasins' + (filterParams.toString() ? '?' + filterParams.toString() : '');
            console.log('Fetching with URL:', url);
            console.log('Filter params:', filterParams.toString());
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        // Process records to flatten color data
                        currentSdasinsData = data.records.map(record => ({
                            ...record,
                            color_name: record.t_colors?.color || ''
                        }));
                        console.log(`Loaded ${currentSdasinsData.length} filtered SDASINS records`);

                        // Apply parent ASIN filter if active, otherwise display filtered data
                        if (currentParentAsinFilter) {
                            console.log(`Applying parent ASIN filter after filtered data load: ${currentParentAsinFilter}`);
                            applyCurrentSdasinsSearch();
                        } else {
                            displaySdasinsData(currentSdasinsData);
                        }
                        setupSdasinsSortingEventListeners();
                        setupSdasinsSearchFilter();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
            });
        }

        function displaySdasinsData(records) {
            const tableBody = document.getElementById('sdasinsTableBody');
            const recordCountDiv = document.getElementById('sdasinsRecordCount');

            // Update record count display
            if (records && records.length > 0) {
                if (currentParentAsinFilter) {
                    recordCountDiv.textContent = `Showing ALL ${records.length.toLocaleString()} records with parent ASIN: ${currentParentAsinFilter}`;
                } else {
                    const totalRecords = currentSdasinsData ? currentSdasinsData.length : 0;
                    recordCountDiv.textContent = `Showing ${records.length.toLocaleString()} of ${totalRecords.toLocaleString()} records`;
                }
            } else {
                recordCountDiv.textContent = '';
            }

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">No SDASINS records found.</td></tr>';
                return;
            }

            // Show loading message for large datasets
            if (records.length > 1000) {
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Rendering ' + records.length.toLocaleString() + ' records...</td></tr>';
            }

            // Use setTimeout to prevent blocking the UI
            setTimeout(() => {
                renderRecordsInBatches(records, tableBody, false);
            }, 10);
        }

        function renderRecordsInBatches(records, tableBody, preserveScroll = false) {
            // Save scroll position if requested
            const scrollPosition = preserveScroll ? (window.pageYOffset || document.documentElement.scrollTop) : null;

            tableBody.innerHTML = '';

            // Render in batches to prevent UI blocking
            const batchSize = 100;
            let currentIndex = 0;

            function renderBatch() {
                const endIndex = Math.min(currentIndex + batchSize, records.length);
                const fragment = document.createDocumentFragment();

                for (let i = currentIndex; i < endIndex; i++) {
                    const record = records[i];
                    const row = createRecordRow(record);
                    fragment.appendChild(row);
                }

                tableBody.appendChild(fragment);
                currentIndex = endIndex;

                if (currentIndex < records.length) {
                    // Continue with next batch
                    setTimeout(renderBatch, 10);
                } else if (preserveScroll && scrollPosition !== null) {
                    // Restore scroll position after all batches are complete
                    setTimeout(() => {
                        window.scrollTo(0, scrollPosition);
                    }, 50);
                }
            }

            renderBatch();
        }

        function createRecordRow(record) {
            const row = document.createElement('tr');

            // ID
            const idCell = document.createElement('td');
            idCell.textContent = record.id;
            row.appendChild(idCell);

            // Parent ASIN (clickable filter + editable)
            const parentAsinCell = document.createElement('td');
            parentAsinCell.className = 'asin-cell';
            parentAsinCell.style.position = 'relative';

            // Create container for both filter span and edit input
            const parentAsinContainer = document.createElement('div');
            parentAsinContainer.style.display = 'flex';
            parentAsinContainer.style.alignItems = 'center';
            parentAsinContainer.style.gap = '5px';

            // Filter span (clickable)
            const parentAsinSpan = document.createElement('span');
            parentAsinSpan.textContent = record.parent_asin || '';
            parentAsinSpan.className = 'parent-asin-filter';
            parentAsinSpan.style.color = '#007bff';
            parentAsinSpan.style.cursor = 'pointer';
            parentAsinSpan.style.textDecoration = 'underline';
            parentAsinSpan.style.flex = '1';
            parentAsinSpan.style.minWidth = '0';
            parentAsinSpan.title = 'Click to filter by this parent ASIN';
            parentAsinSpan.addEventListener('click', function(e) {
                e.stopPropagation();
                if (record.parent_asin) {
                    toggleParentAsinFilter(record.parent_asin);
                }
            });

            // Edit button
            const editButton = document.createElement('button');
            editButton.textContent = '✏️';
            editButton.style.cssText = `
                background: none;
                border: none;
                cursor: pointer;
                font-size: 12px;
                padding: 2px;
                opacity: 0.6;
                flex-shrink: 0;
            `;
            editButton.title = 'Edit parent ASIN';
            editButton.addEventListener('click', function(e) {
                e.stopPropagation();
                editParentAsin(parentAsinCell, record, parentAsinSpan);
            });

            parentAsinContainer.appendChild(parentAsinSpan);
            parentAsinContainer.appendChild(editButton);
            parentAsinCell.appendChild(parentAsinContainer);
            row.appendChild(parentAsinCell);

            // ASIN (clickable link)
            const asinCell = document.createElement('td');
            asinCell.className = 'asin-cell';
            if (record.asin) {
                const asinLink = document.createElement('a');
                asinLink.href = `https://www.amazon.com/placeholder/dp/${record.asin}`;
                asinLink.target = '_blank';
                asinLink.className = 'asin-link';
                asinLink.textContent = record.asin;
                asinCell.appendChild(asinLink);
            } else {
                asinCell.textContent = '';
            }
            row.appendChild(asinCell);

            // Notes (editable with textarea)
            const notesCell = document.createElement('td');
            notesCell.className = 'notes-cell';
            const notesInput = document.createElement('textarea');
            notesInput.className = 'editable-field notes-input';
            notesInput.value = record.notes || '';
            notesInput.dataset.field = 'notes';
            notesInput.dataset.id = record.id;
            notesInput.style.textAlign = 'left';
            notesInput.addEventListener('blur', updateSdasinsField);
            notesInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    this.blur();
                }
            });
            notesCell.appendChild(notesInput);
            row.appendChild(notesCell);

            // Raw Notes (editable with textarea)
            const rawNotesCell = document.createElement('td');
            rawNotesCell.className = 'raw-notes-cell';
            const rawNotesInput = document.createElement('textarea');
            rawNotesInput.className = 'editable-field raw-notes-input';
            rawNotesInput.value = record.raw_notes || '';
            rawNotesInput.dataset.field = 'raw_notes';
            rawNotesInput.dataset.id = record.id;
            rawNotesInput.style.textAlign = 'left';
            rawNotesInput.addEventListener('blur', updateSdasinsField);
            rawNotesInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    this.blur();
                }
            });
            rawNotesCell.appendChild(rawNotesInput);
            row.appendChild(rawNotesCell);

            // Parsed fields (all editable)
            const editableFields = ['parsed_brand', 'parsed_mold', 'parsed_plastic', 'parsed_stamp'];
            editableFields.forEach(field => {
                const cell = document.createElement('td');
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'editable-field compact-input';
                input.value = record[field] || '';
                input.dataset.field = field;
                input.dataset.id = record.id;
                input.style.textAlign = 'left';
                input.addEventListener('blur', updateSdasinsField);
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        this.blur();
                    }
                });
                cell.appendChild(input);
                row.appendChild(cell);
            });

            // Parsed weight fields (numeric)
            const weightFields = ['parsed_min_weight', 'parsed_max_weight'];
            weightFields.forEach(field => {
                const cell = document.createElement('td');
                cell.className = 'weight-cell';
                const input = document.createElement('input');
                input.type = 'number';
                input.step = '0.1';
                input.className = 'editable-field weight-input';
                input.value = record[field] || '';
                input.dataset.field = field;
                input.dataset.id = record.id;
                input.addEventListener('blur', updateSdasinsField);
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        this.blur();
                    }
                });
                cell.appendChild(input);
                row.appendChild(cell);
            });

            // Color (dropdown)
            const colorCell = document.createElement('td');
            colorCell.className = 'color-cell';
            const colorSelect = document.createElement('select');
            colorSelect.className = 'editable-field color-input';
            colorSelect.dataset.field = 'color_id';
            colorSelect.dataset.id = record.id;
            colorSelect.addEventListener('change', updateSdasinsField);

            // Add empty option
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = '';
            colorSelect.appendChild(emptyOption);

            // Add color options
            colorOptions.forEach(color => {
                const option = document.createElement('option');
                option.value = color.id;
                option.textContent = color.color;
                if (record.color_id == color.id) {
                    option.selected = true;
                }
                colorSelect.appendChild(option);
            });

            colorCell.appendChild(colorSelect);
            row.appendChild(colorCell);

            // 30-Day Average Rank (read-only)
            const rankCell = document.createElement('td');
            rankCell.className = 'rank-cell';
            rankCell.textContent = record.so_rank_30day_avg ? Number(record.so_rank_30day_avg).toLocaleString() : '';
            rankCell.style.textAlign = 'right';
            row.appendChild(rankCell);

            // Rank Date (read-only)
            const rankDateCell = document.createElement('td');
            rankDateCell.className = 'date-cell';
            if (record.so_rank_30day_avg_date) {
                const date = new Date(record.so_rank_30day_avg_date);
                rankDateCell.textContent = date.toLocaleDateString();
            } else {
                rankDateCell.textContent = '';
            }
            rankDateCell.style.textAlign = 'center';
            row.appendChild(rankDateCell);

            // FBM Uploaded At (read-only)
            const fbmUploadedCell = document.createElement('td');
            fbmUploadedCell.className = 'date-cell';
            if (record.fbm_uploaded_at) {
                const date = new Date(record.fbm_uploaded_at);
                fbmUploadedCell.textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            } else {
                fbmUploadedCell.textContent = '';
            }
            fbmUploadedCell.style.textAlign = 'center';
            row.appendChild(fbmUploadedCell);

            return row;
        }

        function updateSdasinsField(event) {
            const input = event.target;
            const id = input.dataset.id;
            const field = input.dataset.field;
            const value = input.value;

            // For color dropdown, convert empty string to null
            const finalValue = (field === 'color_id' && value === '') ? null : (value || null);

            // Call API to update the field
            fetch('/api/sdasins/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: parseInt(id),
                    field: field,
                    value: finalValue
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the record in current data
                    const record = currentSdasinsData.find(r => r.id == id);
                    if (record) {
                        record[field] = finalValue;

                        // Check if the updated record still matches current filters
                        if (shouldRecordBeFilteredOut(record)) {
                            // Remove the record from current data and refresh display with scroll preservation
                            currentSdasinsData = currentSdasinsData.filter(r => r.id != id);
                            applyCurrentSdasinsSearchWithScrollPreservation();
                            console.log(`Record ${id} was filtered out after edit`);
                        } else {
                            // Visual feedback for successful update
                            input.style.backgroundColor = '#d4edda';
                            setTimeout(() => {
                                input.style.backgroundColor = '';
                            }, 1000);
                        }
                    }
                } else {
                    console.error('Error updating SDASINS field:', data.error || 'Unknown error');
                    // Reset to original value
                    const record = currentSdasinsData.find(r => r.id == id);
                    if (record) {
                        input.value = record[field] || '';
                    }
                    // Visual feedback for error
                    input.style.backgroundColor = '#f8d7da';
                    setTimeout(() => {
                        input.style.backgroundColor = '';
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error updating SDASINS field:', error.message);
                // Reset to original value
                const record = currentSdasinsData.find(r => r.id == id);
                if (record) {
                    input.value = record[field] || '';
                }
            });
        }

        function shouldRecordBeFilteredOut(record) {
            // If parent ASIN filter is active, ignore all other filters - only check parent ASIN
            if (currentParentAsinFilter) {
                return record.parent_asin !== currentParentAsinFilter;
            }

            // Check if any of the current filters would exclude this record

            // Get current filter states and values
            const filterRankBelow = document.getElementById('filterRankBelow').checked;
            const filterRankValue = parseFloat(document.getElementById('filterRankValue').value) || 0;
            const filterIdAbove = document.getElementById('filterIdAbove').checked;
            const filterIdValue = parseInt(document.getElementById('filterIdValue').value) || 0;
            const filterNotesNotContain = document.getElementById('filterNotesNotContain').checked;
            const filterNotesValue = document.getElementById('filterNotesValue').value || '';
            const filterNotesNotNull = document.getElementById('filterNotesNotNull').checked;
            const filterColorIsNull = document.getElementById('filterColorIsNull').checked;

            // Check rank filter
            if (filterRankBelow) {
                const rank = parseFloat(record.so_rank_30day_avg);
                if (!rank || rank >= filterRankValue) {
                    return true; // Should be filtered out
                }
            }

            // Check ID filter
            if (filterIdAbove) {
                const id = parseInt(record.id);
                if (!id || id <= filterIdValue) {
                    return true; // Should be filtered out
                }
            }

            // Check notes not contain filter
            if (filterNotesNotContain && filterNotesValue) {
                const notes = record.notes || '';
                if (notes.includes(filterNotesValue)) {
                    return true; // Should be filtered out
                }
            }

            // Check notes not null filter
            if (filterNotesNotNull) {
                if (!record.notes || record.notes.trim() === '') {
                    return true; // Should be filtered out
                }
            }

            // Check color is null filter
            if (filterColorIsNull) {
                if (record.color_id !== null && record.color_id !== undefined) {
                    return true; // Should be filtered out
                }
            }

            return false; // Record should stay
        }

        function setupSdasinsSortingEventListeners() {
            document.querySelectorAll('#sdasinsTable .sortable').forEach(header => {
                header.addEventListener('click', function(event) {
                    const column = this.getAttribute('data-column');
                    const type = this.getAttribute('data-type');

                    // Check if Ctrl key is held for cascading sort
                    const isCascading = event.ctrlKey || event.metaKey;

                    if (isCascading) {
                        // Cascading sort: add to sort criteria
                        const existingIndex = sdasinsSortCriteria.findIndex(criteria => criteria.column === column);

                        if (existingIndex >= 0) {
                            // Toggle direction for existing column
                            sdasinsSortCriteria[existingIndex].direction =
                                sdasinsSortCriteria[existingIndex].direction === 'asc' ? 'desc' : 'asc';
                        } else {
                            // Add new sort criteria
                            sdasinsSortCriteria.push({ column: column, direction: 'asc', type: type });
                        }
                    } else {
                        // Regular sort: replace all criteria
                        if (currentSdasinsSortColumn === column) {
                            currentSdasinsSortDirection = currentSdasinsSortDirection === 'asc' ? 'desc' : 'asc';
                        } else {
                            currentSdasinsSortColumn = column;
                            currentSdasinsSortDirection = 'asc';
                        }

                        // Reset cascading criteria to just this column
                        sdasinsSortCriteria = [{ column: column, direction: currentSdasinsSortDirection, type: type }];
                    }

                    sortAndDisplaySdasinsData();
                    updateSdasinsSortIndicators();
                });
            });
        }

        function sortAndDisplaySdasinsData() {
            // Show loading message for large datasets
            if (currentSdasinsData.length > 1000) {
                const tableBody = document.getElementById('sdasinsTableBody');
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Sorting ' + currentSdasinsData.length.toLocaleString() + ' records...</td></tr>';
            }

            // Use setTimeout to prevent UI blocking during sort
            setTimeout(() => {
                const sortedData = [...currentSdasinsData].sort((a, b) => {
                    // Apply cascading sort criteria
                    for (let criteria of sdasinsSortCriteria) {
                        let aVal = a[criteria.column];
                        let bVal = b[criteria.column];

                        // Handle null/undefined values
                        if (aVal === null || aVal === undefined) aVal = '';
                        if (bVal === null || bVal === undefined) bVal = '';

                        // Convert numbers for comparison
                        if (criteria.column === 'id' || criteria.column.includes('weight') || criteria.column === 'color_id' || criteria.column === 'so_rank_30day_avg') {
                            aVal = parseFloat(aVal) || 0;
                            bVal = parseFloat(bVal) || 0;
                        }

                        // Convert dates for comparison
                        if (criteria.column === 'so_rank_30day_avg_date' || criteria.column === 'fbm_uploaded_at') {
                            aVal = aVal ? new Date(aVal).getTime() : 0;
                            bVal = bVal ? new Date(bVal).getTime() : 0;
                        }

                        // Compare values
                        if (aVal < bVal) {
                            return criteria.direction === 'asc' ? -1 : 1;
                        }
                        if (aVal > bVal) {
                            return criteria.direction === 'asc' ? 1 : -1;
                        }
                        // If values are equal, continue to next sort criteria
                    }
                    return 0; // All criteria resulted in equal values
                });

                displaySdasinsData(sortedData);
            }, 10);
        }

        function updateSdasinsSortIndicators() {
            // Remove all sort classes from SDASINS table
            document.querySelectorAll('#sdasinsTable .sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
                header.style.position = 'relative';
                // Remove any existing sort order indicators
                const existingIndicator = header.querySelector('.sort-order');
                if (existingIndicator) {
                    existingIndicator.remove();
                }
            });

            // Add sort indicators for all active sort criteria
            sdasinsSortCriteria.forEach((criteria, index) => {
                const header = document.querySelector(`#sdasinsTable [data-column="${criteria.column}"]`);
                if (header) {
                    header.classList.add(criteria.direction === 'asc' ? 'sort-asc' : 'sort-desc');

                    // Add sort order number for cascading sorts
                    if (sdasinsSortCriteria.length > 1) {
                        const orderIndicator = document.createElement('span');
                        orderIndicator.className = 'sort-order';
                        orderIndicator.textContent = (index + 1).toString();
                        orderIndicator.style.cssText = `
                            position: absolute;
                            top: 2px;
                            right: 2px;
                            background: #007bff;
                            color: white;
                            border-radius: 50%;
                            width: 16px;
                            height: 16px;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                        `;
                        header.appendChild(orderIndicator);
                    }
                }
            });
        }

        function applyCurrentSdasinsSearch() {
            const searchInput = document.getElementById('sdasinsSearchInput');
            const searchTerm = searchInput.value.toLowerCase().trim();

            // If parent ASIN filter is active, show ALL records with that parent ASIN (ignore all other filters)
            if (currentParentAsinFilter) {
                console.log(`Filtering by parent ASIN: ${currentParentAsinFilter}`);
                console.log(`Total records to filter: ${currentSdasinsData.length}`);

                let filteredData = currentSdasinsData.filter(record => {
                    const matches = record.parent_asin === currentParentAsinFilter;
                    if (matches) {
                        console.log(`Match found: ID ${record.id}, Parent ASIN: ${record.parent_asin}`);
                    }
                    return matches;
                });

                console.log(`Found ${filteredData.length} records with parent ASIN: ${currentParentAsinFilter}`);

                // Apply search within parent ASIN results if search term exists
                if (searchTerm) {
                    const searchWords = searchTerm.split(/\s+/);
                    filteredData = filteredData.filter(record => {
                        const searchableText = [
                            record.id?.toString() || '',
                            record.parent_asin || '',
                            record.asin || '',
                            record.notes || '',
                            record.raw_notes || '',
                            record.parsed_brand || '',
                            record.parsed_mold || '',
                            record.parsed_plastic || '',
                            record.parsed_stamp || '',
                            record.parsed_min_weight?.toString() || '',
                            record.parsed_max_weight?.toString() || '',
                            record.color_id?.toString() || '',
                            record.color_name || '',
                            record.so_rank_30day_avg?.toString() || '',
                            record.so_rank_30day_avg_date || '',
                            record.fbm_uploaded_at || ''
                        ].join(' ').toLowerCase();

                        return searchWords.every(word => searchableText.includes(word));
                    });
                }

                // Sort the filtered data by ASIN
                const sortedFilteredData = [...filteredData].sort((a, b) => {
                    const aVal = a.asin || '';
                    const bVal = b.asin || '';
                    return aVal.localeCompare(bVal);
                });

                displaySdasinsData(sortedFilteredData);
                updateParentAsinFilterIndicator();
                return;
            }

            if (!searchTerm) {
                // If search is empty and no parent filter, show all current data sorted
                sortAndDisplaySdasinsData();
                return;
            }

            // Show loading message for large datasets
            if (currentSdasinsData.length > 1000) {
                const tableBody = document.getElementById('sdasinsTableBody');
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Searching ' + currentSdasinsData.length.toLocaleString() + ' records...</td></tr>';
            }

            // Use setTimeout to prevent UI blocking during search
            setTimeout(() => {
                // Split search term into individual words
                const searchWords = searchTerm.split(/\s+/);

                let searchFilteredData = currentSdasinsData.filter(record => {
                    const searchableText = [
                        record.id?.toString() || '',
                        record.parent_asin || '',
                        record.asin || '',
                        record.notes || '',
                        record.raw_notes || '',
                        record.parsed_brand || '',
                        record.parsed_mold || '',
                        record.parsed_plastic || '',
                        record.parsed_stamp || '',
                        record.parsed_min_weight?.toString() || '',
                        record.parsed_max_weight?.toString() || '',
                        record.color_id?.toString() || '',
                        record.color_name || '',
                        record.so_rank_30day_avg?.toString() || '',
                        record.so_rank_30day_avg_date || '',
                        record.fbm_uploaded_at || ''
                    ].join(' ').toLowerCase();

                    // Check if ALL search words are found in the searchable text
                    return searchWords.every(word => searchableText.includes(word));
                });

                // Apply parent ASIN filter if active
                if (currentParentAsinFilter) {
                    searchFilteredData = searchFilteredData.filter(record =>
                        record.parent_asin === currentParentAsinFilter
                    );
                }

                // Sort the filtered data before displaying using cascading criteria
                const sortedFilteredData = [...searchFilteredData].sort((a, b) => {
                    // Apply cascading sort criteria
                    for (let criteria of sdasinsSortCriteria) {
                        let aVal = a[criteria.column];
                        let bVal = b[criteria.column];

                        // Handle null/undefined values
                        if (aVal === null || aVal === undefined) aVal = '';
                        if (bVal === null || bVal === undefined) bVal = '';

                        // Convert numbers for comparison
                        if (criteria.column === 'id' || criteria.column.includes('weight') || criteria.column === 'color_id' || criteria.column === 'so_rank_30day_avg') {
                            aVal = parseFloat(aVal) || 0;
                            bVal = parseFloat(bVal) || 0;
                        }

                        // Convert dates for comparison
                        if (criteria.column === 'so_rank_30day_avg_date' || criteria.column === 'fbm_uploaded_at') {
                            aVal = aVal ? new Date(aVal).getTime() : 0;
                            bVal = bVal ? new Date(bVal).getTime() : 0;
                        }

                        // Compare values
                        if (aVal < bVal) {
                            return criteria.direction === 'asc' ? -1 : 1;
                        }
                        if (aVal > bVal) {
                            return criteria.direction === 'asc' ? 1 : -1;
                        }
                        // If values are equal, continue to next sort criteria
                    }
                    return 0; // All criteria resulted in equal values
                });

                displaySdasinsData(sortedFilteredData);
            }, 10);
        }

        function applyCurrentSdasinsSearchWithScrollPreservation() {
            const searchInput = document.getElementById('sdasinsSearchInput');
            const searchTerm = searchInput.value.toLowerCase().trim();

            // If parent ASIN filter is active, show ALL records with that parent ASIN (ignore all other filters)
            if (currentParentAsinFilter) {
                const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
                console.log(`Scroll preservation: Filtering by parent ASIN: ${currentParentAsinFilter}`);

                let filteredData = currentSdasinsData.filter(record =>
                    record.parent_asin === currentParentAsinFilter
                );

                console.log(`Scroll preservation: Found ${filteredData.length} records with parent ASIN: ${currentParentAsinFilter}`);

                // Apply search within parent ASIN results if search term exists
                if (searchTerm) {
                    const searchWords = searchTerm.split(/\s+/);
                    filteredData = filteredData.filter(record => {
                        const searchableText = [
                            record.id?.toString() || '',
                            record.parent_asin || '',
                            record.asin || '',
                            record.notes || '',
                            record.raw_notes || '',
                            record.parsed_brand || '',
                            record.parsed_mold || '',
                            record.parsed_plastic || '',
                            record.parsed_stamp || '',
                            record.parsed_min_weight?.toString() || '',
                            record.parsed_max_weight?.toString() || '',
                            record.color_id?.toString() || '',
                            record.color_name || '',
                            record.so_rank_30day_avg?.toString() || '',
                            record.so_rank_30day_avg_date || '',
                            record.fbm_uploaded_at || ''
                        ].join(' ').toLowerCase();

                        return searchWords.every(word => searchableText.includes(word));
                    });
                }

                // Sort the filtered data by ASIN
                const sortedFilteredData = [...filteredData].sort((a, b) => {
                    const aVal = a.asin || '';
                    const bVal = b.asin || '';
                    return aVal.localeCompare(bVal);
                });

                displaySdasinsDataWithScrollPreservation(sortedFilteredData, scrollPosition);
                updateParentAsinFilterIndicator();
                return;
            }

            if (!searchTerm) {
                // If search is empty and no parent filter, show all current data sorted with scroll preservation
                sortAndDisplaySdasinsDataWithScrollPreservation();
                return;
            }

            // Save scroll position
            const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            // Use setTimeout to prevent UI blocking during search
            setTimeout(() => {
                // Split search term into individual words
                const searchWords = searchTerm.split(/\s+/);

                let searchFilteredData = currentSdasinsData.filter(record => {
                    const searchableText = [
                        record.id?.toString() || '',
                        record.parent_asin || '',
                        record.asin || '',
                        record.notes || '',
                        record.raw_notes || '',
                        record.parsed_brand || '',
                        record.parsed_mold || '',
                        record.parsed_plastic || '',
                        record.parsed_stamp || '',
                        record.parsed_min_weight?.toString() || '',
                        record.parsed_max_weight?.toString() || '',
                        record.color_id?.toString() || '',
                        record.color_name || '',
                        record.so_rank_30day_avg?.toString() || '',
                        record.so_rank_30day_avg_date || '',
                        record.fbm_uploaded_at || ''
                    ].join(' ').toLowerCase();

                    // Check if ALL search words are found in the searchable text
                    return searchWords.every(word => searchableText.includes(word));
                });

                // Apply parent ASIN filter if active
                if (currentParentAsinFilter) {
                    searchFilteredData = searchFilteredData.filter(record =>
                        record.parent_asin === currentParentAsinFilter
                    );
                }

                // Sort the filtered data before displaying using cascading criteria
                const sortedFilteredData = [...searchFilteredData].sort((a, b) => {
                    // Apply cascading sort criteria
                    for (let criteria of sdasinsSortCriteria) {
                        let aVal = a[criteria.column];
                        let bVal = b[criteria.column];

                        // Handle null/undefined values
                        if (aVal === null || aVal === undefined) aVal = '';
                        if (bVal === null || bVal === undefined) bVal = '';

                        // Convert numbers for comparison
                        if (criteria.column === 'id' || criteria.column.includes('weight') || criteria.column === 'color_id' || criteria.column === 'so_rank_30day_avg') {
                            aVal = parseFloat(aVal) || 0;
                            bVal = parseFloat(bVal) || 0;
                        }

                        // Convert dates for comparison
                        if (criteria.column === 'so_rank_30day_avg_date' || criteria.column === 'fbm_uploaded_at') {
                            aVal = aVal ? new Date(aVal).getTime() : 0;
                            bVal = bVal ? new Date(bVal).getTime() : 0;
                        }

                        // Compare values
                        if (aVal < bVal) {
                            return criteria.direction === 'asc' ? -1 : 1;
                        }
                        if (aVal > bVal) {
                            return criteria.direction === 'asc' ? 1 : -1;
                        }
                        // If values are equal, continue to next sort criteria
                    }
                    return 0; // All criteria resulted in equal values
                });

                // Display with scroll preservation
                displaySdasinsDataWithScrollPreservation(sortedFilteredData, scrollPosition);
            }, 10);
        }

        function sortAndDisplaySdasinsDataWithScrollPreservation() {
            // Save scroll position
            const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            // Use setTimeout to prevent UI blocking during sort
            setTimeout(() => {
                const sortedData = [...currentSdasinsData].sort((a, b) => {
                    // Apply cascading sort criteria
                    for (let criteria of sdasinsSortCriteria) {
                        let aVal = a[criteria.column];
                        let bVal = b[criteria.column];

                        // Handle null/undefined values
                        if (aVal === null || aVal === undefined) aVal = '';
                        if (bVal === null || bVal === undefined) bVal = '';

                        // Convert numbers for comparison
                        if (criteria.column === 'id' || criteria.column.includes('weight') || criteria.column === 'color_id' || criteria.column === 'so_rank_30day_avg') {
                            aVal = parseFloat(aVal) || 0;
                            bVal = parseFloat(bVal) || 0;
                        }

                        // Convert dates for comparison
                        if (criteria.column === 'so_rank_30day_avg_date' || criteria.column === 'fbm_uploaded_at') {
                            aVal = aVal ? new Date(aVal).getTime() : 0;
                            bVal = bVal ? new Date(bVal).getTime() : 0;
                        }

                        // Compare values
                        if (aVal < bVal) {
                            return criteria.direction === 'asc' ? -1 : 1;
                        }
                        if (aVal > bVal) {
                            return criteria.direction === 'asc' ? 1 : -1;
                        }
                        // If values are equal, continue to next sort criteria
                    }
                    return 0; // All criteria resulted in equal values
                });

                displaySdasinsDataWithScrollPreservation(sortedData, scrollPosition);
            }, 10);
        }

        function displaySdasinsDataWithScrollPreservation(records, scrollPosition) {
            const tableBody = document.getElementById('sdasinsTableBody');
            const recordCountDiv = document.getElementById('sdasinsRecordCount');

            // Update record count display
            if (records && records.length > 0) {
                if (currentParentAsinFilter) {
                    recordCountDiv.textContent = `Showing ALL ${records.length.toLocaleString()} records with parent ASIN: ${currentParentAsinFilter}`;
                } else {
                    const totalRecords = currentSdasinsData ? currentSdasinsData.length : 0;
                    recordCountDiv.textContent = `Showing ${records.length.toLocaleString()} of ${totalRecords.toLocaleString()} records`;
                }
            } else {
                recordCountDiv.textContent = '';
            }

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">No SDASINS records found.</td></tr>';
                return;
            }

            // Use setTimeout to prevent blocking the UI
            setTimeout(() => {
                renderRecordsInBatchesWithScrollPreservation(records, tableBody, scrollPosition);
            }, 10);
        }

        function renderRecordsInBatchesWithScrollPreservation(records, tableBody, scrollPosition) {
            tableBody.innerHTML = '';

            // Render in batches to prevent UI blocking
            const batchSize = 100;
            let currentIndex = 0;

            function renderBatch() {
                const endIndex = Math.min(currentIndex + batchSize, records.length);
                const fragment = document.createDocumentFragment();

                for (let i = currentIndex; i < endIndex; i++) {
                    const record = records[i];
                    const row = createRecordRow(record);
                    fragment.appendChild(row);
                }

                tableBody.appendChild(fragment);
                currentIndex = endIndex;

                if (currentIndex < records.length) {
                    // Continue with next batch
                    setTimeout(renderBatch, 10);
                } else {
                    // Restore scroll position after all batches are complete
                    setTimeout(() => {
                        window.scrollTo(0, scrollPosition);
                    }, 50);
                }
            }

            renderBatch();
        }

        function setupSdasinsSearchFilter() {
            const searchInput = document.getElementById('sdasinsSearchInput');
            searchInput.addEventListener('input', function() {
                applyCurrentSdasinsSearch();
            });
        }
    </script>
</body>
</html>
