-- Function to match a disc to all SDASINs in a single operation
CREATE OR REPLACE FUNCTION match_disc_to_all_sdasins(
    disc_id_param INTEGER
)
RETURNS INTEGER AS $$
DECLARE
    disc_rec RECORD;
    match_count INTEGER := 0;
BEGIN
    -- Get the disc record
    SELECT id, mps_id, weight, color_id, sold_date
    INTO disc_rec
    FROM t_discs
    WHERE id = disc_id_param;

    -- Always clear existing join records for this disc before proceeding
    DELETE FROM tjoin_discs_sdasins
    WHERE disc_id = disc_id_param;

    -- Skip matching if disc was sold more than 14 days ago
    IF disc_rec.sold_date IS NOT NULL THEN
        IF disc_rec.sold_date <= (NOW() - INTERVAL '14 days') THEN
            -- Update the disc record to mark that matching has been attempted
            UPDATE t_discs
            SET looked_for_matching_sdasin_at = NOW()
            WHERE id = disc_id_param;

            RETURN 0;
        END IF;
    END IF;

    -- Insert new join records for matching SDASINs
    WITH matching_sdasins AS (
        SELECT
            s.id AS sdasin_id,
            'Matched on MPS, weight range, and color' AS reason
        FROM
            t_sdasins s
            LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
            LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
            LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
            LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
            LEFT JOIN t_mps dm ON disc_rec.mps_id = dm.id
            LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
        WHERE
            s.min_weight IS NOT NULL
            AND s.max_weight IS NOT NULL
            AND (s.mps_id IS NOT NULL OR s.mps_id2 IS NOT NULL)
            AND s.color_id IS NOT NULL
            AND (
                  -- exact mps match
                  (s.mps_id = disc_rec.mps_id OR s.mps_id2 = disc_rec.mps_id)
                  OR
                  -- stock-stamp equivalent: both stamps are SDASIN stock and mold/plastic equal
                  (
                    (sst1.is_sdasin_stock = TRUE AND dst.is_sdasin_stock = TRUE AND sm1.plastic_id = dm.plastic_id AND sm1.mold_id = dm.mold_id)
                    OR
                    (sst2.is_sdasin_stock = TRUE AND dst.is_sdasin_stock = TRUE AND sm2.plastic_id = dm.plastic_id AND sm2.mold_id = dm.mold_id)
                  )
                )
            AND round(disc_rec.weight) >= s.min_weight
            AND round(disc_rec.weight) <= s.max_weight
            AND (disc_rec.color_id = s.color_id OR s.color_id = 23)
    )
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
    SELECT
        disc_id_param,
        sdasin_id,
        reason,
        'system'
    FROM
        matching_sdasins;

    -- Get the number of matches
    GET DIAGNOSTICS match_count = ROW_COUNT;

    -- Update the disc record to mark that matching has been attempted
    UPDATE t_discs
    SET looked_for_matching_sdasin_at = NOW()
    WHERE id = disc_id_param;

    RETURN match_count;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'match_disc_to_all_sdasins function created.';
END $$;
