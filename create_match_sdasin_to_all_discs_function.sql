-- Function to match a SDASIN to all discs in a single operation
CREATE OR REPLACE FUNCTION match_sdasin_to_all_discs(
    sdasin_id_param INTEGER
)
RETURNS INTEGER AS $$
DECLARE
    sdasin_rec RECORD;
    match_count INTEGER := 0;
BEGIN
    -- Get the SDASIN record
    SELECT id, mps_id, mps_id2, min_weight, max_weight, color_id
    INTO sdasin_rec
    FROM t_sdasins
    WHERE id = sdasin_id_param;
    
    -- Delete old join records for this SDASIN
    DELETE FROM tjoin_discs_sdasins
    WHERE sdasin_id = sdasin_id_param;
    
    -- Insert new join records for matching discs
    WITH matching_discs AS (
        SELECT 
            d.id AS disc_id,
            'Matched on MPS, weight range, and color' AS reason
        FROM
            t_discs d
            LEFT JOIN t_mps dm ON d.mps_id = dm.id
            LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
            LEFT JOIN t_mps sm1 ON sdasin_rec.mps_id = sm1.id
            LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
            LEFT JOIN t_mps sm2 ON sdasin_rec.mps_id2 = sm2.id
            LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
        WHERE 
            (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
            AND d.mps_id IS NOT NULL
            AND d.weight IS NOT NULL
            AND d.color_id IS NOT NULL
            AND (
                  -- exact mps match
                  (d.mps_id = sdasin_rec.mps_id OR (sdasin_rec.mps_id2 IS NOT NULL AND d.mps_id = sdasin_rec.mps_id2))
                  OR
                  -- stock-stamp equivalent: both stamps are SDASIN stock and mold/plastic equal
                  (
                    (sdasin_rec.mps_id IS NOT NULL AND sst1.is_sdasin_stock = TRUE AND dst.is_sdasin_stock = TRUE AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                    OR
                    (sdasin_rec.mps_id2 IS NOT NULL AND sst2.is_sdasin_stock = TRUE AND dst.is_sdasin_stock = TRUE AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                  )
                )
            AND round(d.weight) >= sdasin_rec.min_weight
            AND round(d.weight) <= sdasin_rec.max_weight
            AND (d.color_id = sdasin_rec.color_id OR sdasin_rec.color_id = 23)
    )
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
    SELECT 
        disc_id,
        sdasin_id_param,
        reason,
        'system'
    FROM 
        matching_discs;
    
    -- Get the number of matches
    GET DIAGNOSTICS match_count = ROW_COUNT;
    
    -- Update the SDASIN record to mark that matching has been attempted
    UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW()
    WHERE id = sdasin_id_param;
    
    RETURN match_count;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'match_sdasin_to_all_discs function created.';
END $$;
