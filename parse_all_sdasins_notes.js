import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function parseAllSDASINSNotes() {
  try {
    console.log('Starting complete t_sdasins notes parsing...\n');

    // First, get reference data for matching
    console.log('Loading reference data...');
    
    const { data: brands, error: brandsError } = await supabase
      .from('t_brands')
      .select('id, brand');
    
    if (brandsError) {
      console.error('Error fetching brands:', brandsError);
      return;
    }

    const { data: molds, error: moldsError } = await supabase
      .from('t_molds')
      .select('id, mold');
    
    if (moldsError) {
      console.error('Error fetching molds:', moldsError);
      return;
    }

    const { data: plastics, error: plasticsError } = await supabase
      .from('t_plastics')
      .select('id, plastic');
    
    if (plasticsError) {
      console.error('Error fetching plastics:', plasticsError);
      return;
    }

    console.log(`Loaded ${brands.length} brands, ${molds.length} molds, ${plastics.length} plastics\n`);

    // Create lookup maps for faster searching, sorted by length (longest first for better matching)
    const brandMap = new Map();
    brands
      .sort((a, b) => b.brand.length - a.brand.length)
      .forEach(brand => {
        brandMap.set(brand.brand.toLowerCase(), brand);
      });

    const moldMap = new Map();
    molds
      .sort((a, b) => b.mold.length - a.mold.length)
      .forEach(mold => {
        moldMap.set(mold.mold.toLowerCase(), mold);
      });

    const plasticMap = new Map();
    plastics
      .sort((a, b) => b.plastic.length - a.plastic.length)
      .forEach(plastic => {
        plasticMap.set(plastic.plastic.toLowerCase(), plastic);
      });

    // Handle special cases first
    await handleSpecialCases();

    // Get total count of records that need parsing (excluding XXXX records)
    const { count: totalCount, error: countError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .not('notes', 'is', null)
      .not('notes', 'like', 'XXXX%')
      .is('parsed_brand', null);

    if (countError) {
      console.error('Error getting total count:', countError);
      return;
    }

    console.log(`Found ${totalCount} total records to parse\n`);

    const batchSize = 500;
    let offset = 0;
    let totalProcessed = 0;
    let totalUpdated = 0;

    while (offset < totalCount) {
      console.log(`\nProcessing batch ${Math.floor(offset / batchSize) + 1} (records ${offset + 1}-${Math.min(offset + batchSize, totalCount)})...`);

      // Get records that need parsing (have notes but no parsed data, and don't start with XXXX)
      const { data: recordsToParse, error: recordsError } = await supabase
        .from('t_sdasins')
        .select('id, notes, raw_notes, color_id')
        .not('notes', 'is', null)
        .not('notes', 'like', 'XXXX%')
        .is('parsed_brand', null)
        .range(offset, offset + batchSize - 1);

      if (recordsError) {
        console.error('Error fetching records to parse:', recordsError);
        break;
      }

      if (!recordsToParse || recordsToParse.length === 0) {
        console.log('No more records to process.');
        break;
      }

      let batchProcessed = 0;
      let batchUpdated = 0;

      for (const record of recordsToParse) {
        const parsed = parseNotes(record.notes, brandMap, moldMap, plasticMap, record.color_id);

        if (parsed.hasUpdates) {
          const updateData = {};

          // Store original notes in raw_notes if not already stored
          if (!record.raw_notes) {
            updateData.raw_notes = record.notes;
          }

          if (parsed.brand) updateData.parsed_brand = parsed.brand;
          if (parsed.mold) updateData.parsed_mold = parsed.mold;
          if (parsed.plastic) updateData.parsed_plastic = parsed.plastic;
          if (parsed.minWeight) updateData.parsed_min_weight = parsed.minWeight;
          if (parsed.maxWeight) updateData.parsed_max_weight = parsed.maxWeight;
          if (parsed.colorId !== null) updateData.color_id = parsed.colorId;
          if (parsed.remainingNotes !== record.notes) updateData.notes = parsed.remainingNotes;

          const { error: updateError } = await supabase
            .from('t_sdasins')
            .update(updateData)
            .eq('id', record.id);

          if (updateError) {
            console.error(`Error updating record ${record.id}:`, updateError);
          } else {
            batchUpdated++;
            totalUpdated++;
            if (batchUpdated <= 5) { // Only show first 5 updates per batch to avoid spam
              console.log(`  Updated record ${record.id}: ${Object.keys(updateData).join(', ')}`);
            }
          }
        }

        batchProcessed++;
        totalProcessed++;
      }

      console.log(`Batch complete: processed ${batchProcessed}, updated ${batchUpdated}`);
      
      offset += batchSize;
      
      // Small delay to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log(`\nAll batches completed! Total processed: ${totalProcessed}, total updated: ${totalUpdated}`);

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleSpecialCases() {
  console.log('Handling special cases...');

  const specialCases = [
    { contains: 'Elevon', replacement: 'XXXX Elevon' },
    { contains: 'Backpack', replacement: 'XXXX Accessory' },
    { contains: 'Doomsday', replacement: 'XXXX Doomsday' },
    { contains: 'Disc Golf Bag', replacement: 'XXXX Accessory' },
    { contains: 'Starter Set', replacement: 'XXXX Accessory' },
    { contains: 'Disc Storage Rack', replacement: 'XXXX Accessory' },
    { contains: 'Storage Rack', replacement: 'XXXX Accessory' },
    { contains: 'Golf Basket', replacement: 'XXXX Basket' },
    { contains: 'DyeMax', replacement: 'XXXX DyeMax' },
    { contains: 'Rogue Iron', replacement: 'XXXX Rogue Iron' },
    { contains: 'ITHWIU', replacement: 'XXXX ITHWIU' },
    { contains: 'Yikun', replacement: 'XXXX Yikun' },
    { contains: 'Dino Discs', replacement: 'XXXX Dino Discs' },
    { contains: 'Above Level', replacement: 'XXXX Above Level' },
    { contains: 'Bernoulli', replacement: 'XXXX Bernoulli' }
  ];

  for (const specialCase of specialCases) {
    // First get records that match and don't have raw_notes set
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${specialCase.contains}%`)
      .is('raw_notes', null);

    if (fetchError) {
      console.error(`Error fetching records for ${specialCase.contains}:`, fetchError);
      continue;
    }

    // Update each record to preserve original notes in raw_notes
    for (const record of matchingRecords || []) {
      const { error } = await supabase
        .from('t_sdasins')
        .update({ 
          notes: specialCase.replacement,
          raw_notes: record.notes 
        })
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      }
    }

    console.log(`Updated ${matchingRecords?.length || 0} records containing "${specialCase.contains}"`);
  }

  // Handle "Infinite" at beginning
  const { data: infiniteRecords, error: infiniteFetchError } = await supabase
    .from('t_sdasins')
    .select('id, notes, raw_notes')
    .like('notes', 'Infinite%')
    .is('raw_notes', null);

  if (infiniteFetchError) {
    console.error('Error fetching Infinite records:', infiniteFetchError);
  } else {
    for (const record of infiniteRecords || []) {
      const { error } = await supabase
        .from('t_sdasins')
        .update({ 
          notes: 'XXXX Infinite',
          raw_notes: record.notes 
        })
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating Infinite record ${record.id}:`, error);
      }
    }

    console.log(`Updated ${infiniteRecords?.length || 0} records beginning with "Infinite"`);
  }

  console.log('Special cases handled.\n');
}

function parseNotes(notes, brandMap, moldMap, plasticMap, currentColorId = null) {
  if (!notes || notes.startsWith('XXXX')) {
    return { hasUpdates: false };
  }

  // Check for pack listings first (2 pack, 3 pack, etc.)
  const packPatterns = [
    /\d+\s*pack/i, /\d+\s*Pack/i, /\d+-pack/i, /\d+-Pack/i,
    /Disc Golf Set/i, /Disc Set/i, /Battle Pack/i, /Beginner Set/i
  ];
  for (const pattern of packPatterns) {
    if (pattern.test(notes)) {
      return {
        hasUpdates: true,
        remainingNotes: 'XXXX Pack',
        brand: null,
        mold: null,
        plastic: null,
        minWeight: null,
        maxWeight: null,
        colorId: null
      };
    }
  }

  const result = {
    brand: null,
    mold: null,
    plastic: null,
    minWeight: null,
    maxWeight: null,
    colorId: null,
    remainingNotes: notes,
    hasUpdates: false
  };

  let workingNotes = notes;

  // Handle color_id for "Colors Will Vary" and "Colors May Vary"
  if (!currentColorId) {
    const colorPhrases = ['Colors Will Vary', 'Colors May Vary'];
    for (const phrase of colorPhrases) {
      const regex = new RegExp(escapeRegex(phrase), 'gi');
      if (regex.test(workingNotes)) {
        result.colorId = 23;
        workingNotes = workingNotes.replace(regex, '').trim();
        result.hasUpdates = true;
        break;
      }
    }
  }

  // Remove filler text phrases (longest to shortest)
  const fillerPhrases = [
    'Straight-Flying & Lightweight Frisbee Golf Putter',
    'Overstable Frisbee Golf Putt and Approach Disc',
    'Supercolor Gallery Fire Mid-Range Golf Disc',
    'Great Disc Golf Disc for Beginners',
    'Beginner Friendly Frisbee Golf Disc',
    'Putt and Approach Disc Golf Disc',
    'Putt and Approach Golf Disc',
    'Made for Putt & Approach Shots',
    'Long and Fast Disc Golf Driver',
    'Disc Golf Distance Driver',
    'Understable Disc Golf Driver',
    'Straight Disc Golf Putter',
    'Beginner Disc Golf Frisbee',
    'Disc Golf Fairway Driver',
    'Discs Mid-Range Disc Golf Disc',
    'Distance Driver Golf Disc',
    'Fairway Driver Disc Golf',
    'Driver Disc Golf Disc',
    'Rolling Mid-Range Disc',
    'High Speed Long Range',
    'Mid-Range Golf Disc',
    'Max Distance Driver',
    'Golf Disc Driver',
    'Disc Golf Midrange',
    'Disc Golf Driver',
    'Disc Golf Putter',
    'Lightweight & Accurate',
    'Firm Grippy Plastic',
    'Extremely Durable',
    'Slightly Overstable',
    'Floats in Water',
    'Understable'
  ];

  for (const phrase of fillerPhrases) {
    const regex = new RegExp(escapeRegex(phrase), 'gi');
    if (regex.test(workingNotes)) {
      workingNotes = workingNotes.replace(regex, '').trim();
      result.hasUpdates = true;
    }
  }

  // Extract weight ranges (e.g., "173-174g", "170-175g")
  const weightMatch = workingNotes.match(/(\d{3})-(\d{3})g?/);
  if (weightMatch) {
    result.minWeight = parseInt(weightMatch[1]);
    result.maxWeight = parseInt(weightMatch[2]);
    workingNotes = workingNotes.replace(weightMatch[0], '').trim();
    result.hasUpdates = true;
  }

  // Try to find brand matches (use word boundaries for better matching)
  for (const [brandName, brandData] of brandMap) {
    const brandRegex = new RegExp(`\\b${escapeRegex(brandName)}\\b`, 'gi');
    if (brandRegex.test(workingNotes)) {
      result.brand = brandData.brand;
      workingNotes = workingNotes.replace(brandRegex, '').trim();
      result.hasUpdates = true;
      break;
    }
  }

  // Try to find mold matches (use word boundaries, minimum 3 characters to avoid false matches)
  for (const [moldName, moldData] of moldMap) {
    if (moldName.length >= 3) {
      const moldRegex = new RegExp(`\\b${escapeRegex(moldName)}\\b`, 'gi');
      if (moldRegex.test(workingNotes)) {
        result.mold = moldData.mold;
        workingNotes = workingNotes.replace(moldRegex, '').trim();
        result.hasUpdates = true;
        break;
      }
    }
  }

  // Try to find plastic matches (use word boundaries, minimum 3 characters)
  for (const [plasticName, plasticData] of plasticMap) {
    if (plasticName.length >= 3) {
      const plasticRegex = new RegExp(`\\b${escapeRegex(plasticName)}\\b`, 'gi');
      if (plasticRegex.test(workingNotes)) {
        result.plastic = plasticData.plastic;
        workingNotes = workingNotes.replace(plasticRegex, '').trim();
        result.hasUpdates = true;
        break;
      }
    }
  }

  // Clean up remaining notes
  result.remainingNotes = workingNotes
    .replace(/\s+/g, ' ')
    .replace(/^\s*[-|,]\s*/, '')
    .replace(/\s*[-|,]\s*$/, '')
    .trim();

  return result;
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Run the parser
parseAllSDASINSNotes();
