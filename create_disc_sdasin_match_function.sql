-- Function to check if a disc matches a SDASIN
CREATE OR REPLACE FUNCTION check_disc_sdasin_match(
    disc_id_param INTEGER,
    sdasin_id_param INTEGER
)
RETURNS TABLE (
    reason TEXT
) AS $$
DECLARE
    disc_rec RECORD;
    sdasin_rec RECORD;
    v_reason TEXT;
BEGIN
    -- Get the disc record
    SELECT d.id, d.mps_id, d.weight, d.color_id
    INTO disc_rec
    FROM t_discs d
    WHERE d.id = disc_id_param;
    
    -- Get the SDASIN record
    SELECT s.id, s.mps_id, s.mps_id2, s.min_weight, s.max_weight, s.color_id
    INTO sdasin_rec
    FROM t_sdasins s
    WHERE s.id = sdasin_id_param;
    
    -- Check if the disc matches the SDASIN
    v_reason := NULL;
    
    -- Check MPS match: exact or stock-stamp equivalent
    IF (
         disc_rec.mps_id = sdasin_rec.mps_id OR disc_rec.mps_id = sdasin_rec.mps_id2
       )
       OR EXISTS (
         SELECT 1
         FROM t_mps dm
         JOIN t_stamps dst ON dm.stamp_id = dst.id
         LEFT JOIN t_mps sm1 ON sdasin_rec.mps_id = sm1.id
         LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
         LEFT JOIN t_mps sm2 ON sdasin_rec.mps_id2 = sm2.id
         LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
         WHERE dm.id = disc_rec.mps_id
           AND (
             (sdasin_rec.mps_id IS NOT NULL AND sst1.is_sdasin_stock = TRUE AND dst.is_sdasin_stock = TRUE AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
             OR
             (sdasin_rec.mps_id2 IS NOT NULL AND sst2.is_sdasin_stock = TRUE AND dst.is_sdasin_stock = TRUE AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
           )
       )
    THEN
        -- Check weight range
        IF round(disc_rec.weight) >= sdasin_rec.min_weight AND round(disc_rec.weight) <= sdasin_rec.max_weight THEN
            -- Check color match
            IF disc_rec.color_id = sdasin_rec.color_id OR sdasin_rec.color_id = 23 THEN
                v_reason := 'Matched on MPS, weight range, and color';
            END IF;
        END IF;
    END IF;
    
    -- Return the reason if there's a match
    IF v_reason IS NOT NULL THEN
        RETURN QUERY SELECT v_reason;
    ELSE
        RETURN QUERY SELECT NULL::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Disc-SDASIN match function created.';
END $$;
