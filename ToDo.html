<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToDo Management</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            width: 100%;
            box-sizing: border-box;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        header h1 {
            color: white;
            margin: 0;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .card-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 {
            margin: 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #ddd;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        .placeholder-content {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-style: italic;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .data-table th {
            background-color: #2c3e50;
            color: white;
            text-align: left;
            padding: 12px;
            border: 1px solid #ddd;
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        .data-table th:hover {
            background-color: #34495e;
        }
        .data-table th.sortable::after {
            content: ' ↕';
            opacity: 0.5;
            margin-left: 5px;
        }
        .data-table th.sort-asc::after {
            content: ' ↑';
            opacity: 1;
        }
        .data-table th.sort-desc::after {
            content: ' ↓';
            opacity: 1;
        }
        .data-table td {
            padding: 10px 12px;
            border: 1px solid #ddd;
            text-align: right;
        }
        .data-table td:first-child,
        .data-table td:nth-child(2) {
            text-align: left;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .data-table tr:hover {
            background-color: #e9ecef;
        }
        .action-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0;
        }
        .action-btn:hover {
            background-color: #c0392b;
        }
        .loading-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .refresh-btn {
            background-color: #27ae60;
            margin-bottom: 15px;
        }
        .refresh-btn:hover {
            background-color: #229954;
        }
        .editable-field {
            background: none;
            border: 1px solid transparent;
            padding: 4px 8px;
            width: 100%;
            text-align: right;
            font-family: inherit;
            font-size: inherit;
        }
        .editable-field:hover {
            border-color: #3498db;
            background-color: #f8f9fa;
        }
        .editable-field:focus {
            outline: none;
            border-color: #3498db;
            background-color: white;
            box-shadow: 0 0 3px rgba(52, 152, 219, 0.3);
        }
        .verify-btn {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin: 0;
        }
        .verify-btn:hover {
            background-color: #229954;
        }
        .verify-btn:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }




    </style>
</head>
<body>
    <header>
        <h1>ToDo Management Dashboard</h1>
        <p>Manage tasks and items across different data types</p>
    </header>



    <div class="tabs">
        <div class="tab active" data-tab="plastic">Plastic</div>
        <div class="tab" data-tab="mold">Mold</div>
        <div class="tab" data-tab="mps">MPS</div>
        <div class="tab" data-tab="osl">OSL</div>
        <div class="tab" data-tab="disc">Disc</div>
        <div class="tab" data-tab="mps-review">MPS Review</div>
    </div>

    <div id="plastic" class="tab-content active">
        <div class="card">
            <div class="card-header">
                <h2>Plastic Pricing Review</h2>
                <button id="refreshPlasticPricingBtn" class="refresh-btn">Refresh Data</button>
            </div>
            <div>
                <p>Review and update plastic pricing information. Edit price fields directly in the table and verify cost/price data.</p>

                <!-- Search bar -->
                <div style="margin-bottom: 15px;">
                    <input type="text" id="plasticSearchInput" placeholder="Search plastics..." style="padding: 8px; width: 300px; border: 1px solid #ddd; border-radius: 4px;">
                </div>

                <div id="plasticPricingTableContainer">
                    <table class="data-table" id="plasticPricingTable">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="id" data-type="number">ID</th>
                                <th class="sortable" data-column="plastic" data-type="text">Plastic</th>
                                <th class="sortable" data-column="discs_in_stock_and_uploaded" data-type="number">Discs in Stock & Uploaded</th>
                                <th class="sortable" data-column="val_order_cost" data-type="number">Order Cost</th>
                                <th class="sortable" data-column="val_map_price" data-type="number">MAP Price</th>
                                <th class="sortable" data-column="val_retail_price" data-type="number">Retail Price</th>
                                <th class="sortable" data-column="val_msrp" data-type="number">MSRP</th>
                                <th class="sortable" data-column="val_max_amazon_price" data-type="number">Max Amazon Price</th>
                                <th class="sortable" data-column="price_cost_verified_at" data-type="date">Price Verified At</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="plasticPricingTableBody">
                            <tr>
                                <td colspan="10" class="loading-message">Click "Refresh Data" to load plastic pricing data...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="mold" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Mold ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Mold Management</h3>
                <p>This section will contain mold-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mps" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>MPS ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>MPS Management</h3>
                <p>This section will contain MPS-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="osl" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>OSL ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>OSL Management</h3>
                <p>This section will contain OSL-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="disc" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Disc ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Disc Management</h3>
                <p>This section will contain disc-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mps-review" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>MPS Review - Active MPS with No Stock</h2>
            </div>
            <div>
                <p>Review active MPS records that currently have no discs in stock. These may be candidates for marking as inactive.</p>
                <button id="refreshMpsReviewBtn" class="refresh-btn">Refresh Data</button>

                <div id="mpsReviewTableContainer">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="id" data-type="number">MPS ID</th>
                                <th class="sortable" data-column="g_code" data-type="text">G Code</th>
                                <th class="sortable" data-column="sold_date_last" data-type="date">Last Sold Date</th>
                                <th class="sortable" data-column="received_date_last" data-type="date">Last Received Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="mpsReviewTableBody">
                            <tr>
                                <td colspan="5" class="loading-message">Click "Refresh Data" to load MPS records...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // MPS Review functionality
        document.getElementById('refreshMpsReviewBtn').addEventListener('click', function() {
            loadMpsReviewData();
        });

        // Plastic Pricing functionality
        document.getElementById('refreshPlasticPricingBtn').addEventListener('click', function() {
            loadPlasticPricingData();
        });



        // Global variable to store current data for sorting
        let currentMpsData = [];
        let currentSortColumn = 'id';
        let currentSortDirection = 'asc';

        // Global variables for plastic pricing
        let currentPlasticData = [];
        let currentPlasticSortColumn = 'id';
        let currentPlasticSortDirection = 'asc';



        function loadMpsReviewData() {
            const tableBody = document.getElementById('mpsReviewTableBody');
            tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Loading MPS review data...</td></tr>';

            // Call API to get MPS review data
            fetch('/api/mps-review')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        currentMpsData = data.records;
                        sortAndDisplayData();
                        setupSortingEventListeners();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        function displayMpsReviewData(records) {
            const tableBody = document.getElementById('mpsReviewTableBody');

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">No active MPS records with zero stock found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';

            records.forEach(record => {
                const row = document.createElement('tr');

                // MPS ID
                const idCell = document.createElement('td');
                idCell.textContent = record.id;
                row.appendChild(idCell);

                // G Code
                const codeCell = document.createElement('td');
                codeCell.textContent = record.g_code || '';
                row.appendChild(codeCell);

                // Last Sold Date
                const soldDateCell = document.createElement('td');
                soldDateCell.textContent = record.sold_date_last ? new Date(record.sold_date_last).toLocaleDateString() : '';
                row.appendChild(soldDateCell);

                // Last Received Date
                const receivedDateCell = document.createElement('td');
                receivedDateCell.textContent = record.received_date_last ? new Date(record.received_date_last).toLocaleDateString() : '';
                row.appendChild(receivedDateCell);

                // Action button
                const actionCell = document.createElement('td');
                const actionBtn = document.createElement('button');
                actionBtn.textContent = 'Mark Inactive';
                actionBtn.className = 'action-btn';
                actionBtn.onclick = () => markMpsInactive(record.id);
                actionCell.appendChild(actionBtn);
                row.appendChild(actionCell);

                tableBody.appendChild(row);
            });
        }

        function markMpsInactive(mpsId) {
            // Call API to mark MPS as inactive
            fetch('/api/mps-mark-inactive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mpsId: mpsId }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the record from current data and refresh display
                    currentMpsData = currentMpsData.filter(record => record.id !== mpsId);
                    sortAndDisplayData();
                } else {
                    console.error('Error marking MPS as inactive:', data.error || 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Error marking MPS as inactive:', error.message);
            });
        }

        function setupSortingEventListeners() {
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    const type = this.getAttribute('data-type');

                    // Toggle sort direction if clicking the same column
                    if (currentSortColumn === column) {
                        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSortColumn = column;
                        currentSortDirection = 'asc';
                    }

                    sortAndDisplayData();
                    updateSortIndicators();
                });
            });
        }

        function sortAndDisplayData() {
            const sortedData = [...currentMpsData].sort((a, b) => {
                let aVal = a[currentSortColumn];
                let bVal = b[currentSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentSortColumn === 'id') {
                    aVal = parseInt(aVal) || 0;
                    bVal = parseInt(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayMpsReviewData(sortedData);
        }

        function updateSortIndicators() {
            // Remove all sort classes
            document.querySelectorAll('.sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Add sort class to current column
            const currentHeader = document.querySelector(`[data-column="${currentSortColumn}"]`);
            if (currentHeader) {
                currentHeader.classList.add(currentSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        }

        // Plastic Pricing Functions
        function loadPlasticPricingData() {
            const tableBody = document.getElementById('plasticPricingTableBody');
            tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Loading plastic pricing data...</td></tr>';

            // Call API to get plastic pricing data
            fetch('/api/plastic-pricing')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        currentPlasticData = data.records;
                        sortAndDisplayPlasticData();
                        setupPlasticSortingEventListeners();
                        setupPlasticSearchFilter();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        function displayPlasticPricingData(records) {
            const tableBody = document.getElementById('plasticPricingTableBody');

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">No plastic records found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';

            records.forEach(record => {
                const row = document.createElement('tr');

                // ID
                const idCell = document.createElement('td');
                idCell.textContent = record.id;
                row.appendChild(idCell);

                // Plastic name
                const plasticCell = document.createElement('td');
                plasticCell.textContent = record.plastic || '';
                row.appendChild(plasticCell);

                // Discs in stock and uploaded
                const stockCell = document.createElement('td');
                stockCell.textContent = record.discs_in_stock_and_uploaded || 0;
                stockCell.style.textAlign = 'right';
                row.appendChild(stockCell);

                // Editable price fields
                const priceFields = ['val_order_cost', 'val_map_price', 'val_retail_price', 'val_msrp', 'val_max_amazon_price'];
                priceFields.forEach(field => {
                    const cell = document.createElement('td');
                    const input = document.createElement('input');
                    input.type = 'number';
                    input.step = '0.01';
                    input.className = 'editable-field';
                    input.value = record[field] || '';
                    input.dataset.field = field;
                    input.dataset.id = record.id;
                    input.addEventListener('blur', updatePlasticField);
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            this.blur();
                        }
                    });
                    cell.appendChild(input);
                    row.appendChild(cell);
                });

                // Price verified at
                const verifiedCell = document.createElement('td');
                verifiedCell.textContent = record.price_cost_verified_at ? new Date(record.price_cost_verified_at).toLocaleDateString() : '';
                row.appendChild(verifiedCell);

                // Action button
                const actionCell = document.createElement('td');
                const verifyBtn = document.createElement('button');
                verifyBtn.textContent = 'Verify Cost/Price';
                verifyBtn.className = 'verify-btn';
                verifyBtn.onclick = () => verifyPlasticCostPrice(record.id);
                actionCell.appendChild(verifyBtn);
                row.appendChild(actionCell);

                tableBody.appendChild(row);
            });
        }

        function updatePlasticField(event) {
            const input = event.target;
            const id = input.dataset.id;
            const field = input.dataset.field;
            const value = input.value;

            // Call API to update the field
            fetch('/api/plastic-pricing/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: parseInt(id),
                    field: field,
                    value: value ? parseFloat(value) : null
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the record in current data
                    const record = currentPlasticData.find(r => r.id == id);
                    if (record) {
                        record[field] = value ? parseFloat(value) : null;
                    }
                    // Visual feedback
                    input.style.backgroundColor = '#d4edda';
                    setTimeout(() => {
                        input.style.backgroundColor = '';
                    }, 1000);
                } else {
                    console.error('Error updating plastic field:', data.error || 'Unknown error');
                    // Reset to original value
                    const record = currentPlasticData.find(r => r.id == id);
                    if (record) {
                        input.value = record[field] || '';
                    }
                    // Visual feedback for error
                    input.style.backgroundColor = '#f8d7da';
                    setTimeout(() => {
                        input.style.backgroundColor = '';
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error updating plastic field:', error.message);
                // Reset to original value
                const record = currentPlasticData.find(r => r.id == id);
                if (record) {
                    input.value = record[field] || '';
                }
            });
        }

        function verifyPlasticCostPrice(plasticId) {
            // Call API to verify cost/price
            fetch('/api/plastic-pricing/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: plasticId }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the record in current data
                    const record = currentPlasticData.find(r => r.id == plasticId);
                    if (record) {
                        record.price_cost_verified_at = new Date().toISOString();
                    }
                    // Reapply the current search filter instead of showing all data
                    applyCurrentPlasticSearch();
                } else {
                    console.error('Error verifying plastic cost/price:', data.error || 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Error verifying plastic cost/price:', error.message);
            });
        }

        function setupPlasticSortingEventListeners() {
            document.querySelectorAll('#plasticPricingTable .sortable').forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    const type = this.getAttribute('data-type');

                    // Toggle sort direction if clicking the same column
                    if (currentPlasticSortColumn === column) {
                        currentPlasticSortDirection = currentPlasticSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentPlasticSortColumn = column;
                        currentPlasticSortDirection = 'asc';
                    }

                    sortAndDisplayPlasticData();
                    updatePlasticSortIndicators();
                });
            });
        }

        function sortAndDisplayPlasticData() {
            const sortedData = [...currentPlasticData].sort((a, b) => {
                let aVal = a[currentPlasticSortColumn];
                let bVal = b[currentPlasticSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentPlasticSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentPlasticSortColumn === 'id' || currentPlasticSortColumn.startsWith('val_') || currentPlasticSortColumn === 'discs_in_stock_and_uploaded') {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentPlasticSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentPlasticSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayPlasticPricingData(sortedData);
        }

        function updatePlasticSortIndicators() {
            // Remove all sort classes from plastic table
            document.querySelectorAll('#plasticPricingTable .sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Add sort class to current column
            const currentHeader = document.querySelector(`#plasticPricingTable [data-column="${currentPlasticSortColumn}"]`);
            if (currentHeader) {
                currentHeader.classList.add(currentPlasticSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        }

        function applyCurrentPlasticSearch() {
            const searchInput = document.getElementById('plasticSearchInput');
            const searchTerm = searchInput.value.toLowerCase().trim();

            if (!searchTerm) {
                // If search is empty, show all data sorted
                sortAndDisplayPlasticData();
                return;
            }

            // Split search term into individual words
            const searchWords = searchTerm.split(/\s+/);

            const filteredData = currentPlasticData.filter(record => {
                const plasticName = (record.plastic || '').toLowerCase();
                const plasticId = (record.id || '').toString();

                // Check if ALL search words are found in either plastic name or ID
                return searchWords.every(word =>
                    plasticName.includes(word) || plasticId.includes(word)
                );
            });

            // Sort the filtered data before displaying
            const sortedFilteredData = [...filteredData].sort((a, b) => {
                let aVal = a[currentPlasticSortColumn];
                let bVal = b[currentPlasticSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentPlasticSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentPlasticSortColumn === 'id' || currentPlasticSortColumn.startsWith('val_') || currentPlasticSortColumn === 'discs_in_stock_and_uploaded') {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentPlasticSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentPlasticSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayPlasticPricingData(sortedFilteredData);
        }

        function setupPlasticSearchFilter() {
            const searchInput = document.getElementById('plasticSearchInput');
            searchInput.addEventListener('input', function() {
                applyCurrentPlasticSearch();
            });
        }


    </script>
</body>
</html>
            const recordCountDiv = document.getElementById('sdasinsRecordCount');
            if (currentParentAsinFilter) {
                // Update all parent ASIN spans to show which one is active
                document.querySelectorAll('.parent-asin-filter').forEach(span => {
                    if (span.textContent === currentParentAsinFilter) {
                        span.style.backgroundColor = '#007bff';
                        span.style.color = 'white';
                        span.style.padding = '2px 4px';
                        span.style.borderRadius = '3px';
                        span.title = 'Click to remove parent ASIN filter';
                    } else {
                        span.style.backgroundColor = '';
                        span.style.color = '#007bff';
                        span.style.padding = '';
                        span.style.borderRadius = '';
                        span.title = 'Click to filter by this parent ASIN';
                    }
                });
            } else {
                // Reset all parent ASIN spans
                document.querySelectorAll('.parent-asin-filter').forEach(span => {
                    span.style.backgroundColor = '';
                    span.style.color = '#007bff';
                    span.style.padding = '';
                    span.style.borderRadius = '';
                    span.title = 'Click to filter by this parent ASIN';
                });
            }
        }

        function loadColorOptions() {
            return fetch('/api/colors')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.colors) {
                        colorOptions = data.colors;
                        console.log(`Loaded ${colorOptions.length} color options`);
                        return colorOptions;
                    } else {
                        console.error('Error loading color options:', data.error || 'Unknown error');
                        return [];
                    }
                })
                .catch(error => {
                    console.error('Error loading color options:', error.message);
                    return [];
                });
        }

        function loadSdasinsData() {
            const tableBody = document.getElementById('sdasinsTableBody');
            tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Loading color options and SDASINS data...</td></tr>';

            // Load color options first, then SDASINS data
            loadColorOptions().then(() => {
                // Call API to get SDASINS data without filters
                fetch('/api/sdasins')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        // Process records to flatten color data
                        currentSdasinsData = data.records.map(record => ({
                            ...record,
                            color_name: record.t_colors?.color || ''
                        }));
                        console.log(`Loaded ${currentSdasinsData.length} SDASINS records`);

                        // Apply parent ASIN filter if active, otherwise display all data
                        if (currentParentAsinFilter) {
                            console.log(`Applying parent ASIN filter after data load: ${currentParentAsinFilter}`);
                            applyCurrentSdasinsSearch();
                            updateSdasinsSortIndicators(); // Update sort indicators for ASIN sort
                        } else {
                            displaySdasinsData(currentSdasinsData);
                        }
                        setupSdasinsSortingEventListeners();
                        setupSdasinsSearchFilter();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
            });
        }

        function loadSdasinsDataWithFilters() {
            const tableBody = document.getElementById('sdasinsTableBody');
            tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Loading color options and filtered SDASINS data...</td></tr>';

            // Load color options first if not already loaded
            const colorPromise = colorOptions.length > 0 ? Promise.resolve() : loadColorOptions();

            colorPromise.then(() => {

            // Build filter parameters
            const filterParams = new URLSearchParams();

            if (document.getElementById('filterRankBelow').checked) {
                const rankValue = document.getElementById('filterRankValue').value;
                if (rankValue) {
                    filterParams.append('rankBelow', rankValue);
                }
            }

            if (document.getElementById('filterIdAbove').checked) {
                const idValue = document.getElementById('filterIdValue').value;
                if (idValue) {
                    filterParams.append('idAbove', idValue);
                }
            }

            if (document.getElementById('filterNotesNotContain').checked) {
                const notesValue = document.getElementById('filterNotesValue').value;
                if (notesValue) {
                    filterParams.append('notesNotContain', notesValue);
                }
            }

            if (document.getElementById('filterNotesNotNull').checked) {
                filterParams.append('notesNotNull', 'true');
            }

            if (document.getElementById('filterColorIsNull').checked) {
                filterParams.append('colorIsNull', 'true');
            }

            // Call API with filter parameters
            const url = '/api/sdasins' + (filterParams.toString() ? '?' + filterParams.toString() : '');
            console.log('Fetching with URL:', url);
            console.log('Filter params:', filterParams.toString());
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        // Process records to flatten color data
                        currentSdasinsData = data.records.map(record => ({
                            ...record,
                            color_name: record.t_colors?.color || ''
                        }));
                        console.log(`Loaded ${currentSdasinsData.length} filtered SDASINS records`);

                        // Apply parent ASIN filter if active, otherwise display filtered data
                        if (currentParentAsinFilter) {
                            console.log(`Applying parent ASIN filter after filtered data load: ${currentParentAsinFilter}`);
                            applyCurrentSdasinsSearch();
                        } else {
                            displaySdasinsData(currentSdasinsData);
                        }
                        setupSdasinsSortingEventListeners();
                        setupSdasinsSearchFilter();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
            });
        }

        function displaySdasinsData(records) {
            const tableBody = document.getElementById('sdasinsTableBody');
            const recordCountDiv = document.getElementById('sdasinsRecordCount');

            // Update record count display
            if (records && records.length > 0) {
                if (currentParentAsinFilter) {
                    recordCountDiv.textContent = `Showing ALL ${records.length.toLocaleString()} records with parent ASIN: ${currentParentAsinFilter}`;
                } else {
                    const totalRecords = currentSdasinsData ? currentSdasinsData.length : 0;
                    recordCountDiv.textContent = `Showing ${records.length.toLocaleString()} of ${totalRecords.toLocaleString()} records`;
                }
            } else {
                recordCountDiv.textContent = '';
            }

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">No SDASINS records found.</td></tr>';
                return;
            }

            // Show loading message for large datasets
            if (records.length > 1000) {
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Rendering ' + records.length.toLocaleString() + ' records...</td></tr>';
            }

            // Use setTimeout to prevent blocking the UI
            setTimeout(() => {
                renderRecordsInBatches(records, tableBody, false);
            }, 10);
        }

        function renderRecordsInBatches(records, tableBody, preserveScroll = false) {
            // Save scroll position if requested
            const scrollPosition = preserveScroll ? (window.pageYOffset || document.documentElement.scrollTop) : null;

            tableBody.innerHTML = '';

            // Render in batches to prevent UI blocking
            const batchSize = 100;
            let currentIndex = 0;

            function renderBatch() {
                const endIndex = Math.min(currentIndex + batchSize, records.length);
                const fragment = document.createDocumentFragment();

                for (let i = currentIndex; i < endIndex; i++) {
                    const record = records[i];
                    const row = createRecordRow(record);
                    fragment.appendChild(row);
                }

                tableBody.appendChild(fragment);
                currentIndex = endIndex;

                if (currentIndex < records.length) {
                    // Continue with next batch
                    setTimeout(renderBatch, 10);
                } else if (preserveScroll && scrollPosition !== null) {
                    // Restore scroll position after all batches are complete
                    setTimeout(() => {
                        window.scrollTo(0, scrollPosition);
                    }, 50);
                }
            }

            renderBatch();
        }

        function createRecordRow(record) {
            const row = document.createElement('tr');

            // ID
            const idCell = document.createElement('td');
            idCell.textContent = record.id;
            row.appendChild(idCell);

            // Parent ASIN (clickable filter + editable)
            const parentAsinCell = document.createElement('td');
            parentAsinCell.className = 'asin-cell';
            parentAsinCell.style.position = 'relative';

            // Create container for both filter span and edit input
            const parentAsinContainer = document.createElement('div');
            parentAsinContainer.style.display = 'flex';
            parentAsinContainer.style.alignItems = 'center';
            parentAsinContainer.style.gap = '5px';

            // Filter span (clickable)
            const parentAsinSpan = document.createElement('span');
            parentAsinSpan.textContent = record.parent_asin || '';
            parentAsinSpan.className = 'parent-asin-filter';
            parentAsinSpan.style.color = '#007bff';
            parentAsinSpan.style.cursor = 'pointer';
            parentAsinSpan.style.textDecoration = 'underline';
            parentAsinSpan.style.flex = '1';
            parentAsinSpan.style.minWidth = '0';
            parentAsinSpan.title = 'Click to filter by this parent ASIN';
            parentAsinSpan.addEventListener('click', function(e) {
                e.stopPropagation();
                if (record.parent_asin) {
                    toggleParentAsinFilter(record.parent_asin);
                }
            });

            // Edit button
            const editButton = document.createElement('button');
            editButton.textContent = '✏️';
            editButton.style.cssText = `
                background: none;
                border: none;
                cursor: pointer;
                font-size: 12px;
                padding: 2px;
                opacity: 0.6;
                flex-shrink: 0;
            `;
            editButton.title = 'Edit parent ASIN';
            editButton.addEventListener('click', function(e) {
                e.stopPropagation();
                editParentAsin(parentAsinCell, record, parentAsinSpan);
            });

            parentAsinContainer.appendChild(parentAsinSpan);
            parentAsinContainer.appendChild(editButton);
            parentAsinCell.appendChild(parentAsinContainer);
            row.appendChild(parentAsinCell);

            // ASIN (clickable link)
            const asinCell = document.createElement('td');
            asinCell.className = 'asin-cell';
            if (record.asin) {
                const asinLink = document.createElement('a');
                asinLink.href = `https://www.amazon.com/placeholder/dp/${record.asin}`;
                asinLink.target = '_blank';
                asinLink.className = 'asin-link';
                asinLink.textContent = record.asin;
                asinLink.addEventListener('click', function(e) {
                    // Copy ASIN to clipboard when clicked
                    copyToClipboard(record.asin);
                });
                asinCell.appendChild(asinLink);
            } else {
                asinCell.textContent = '';
            }
            row.appendChild(asinCell);

            // Notes (editable with textarea)
            const notesCell = document.createElement('td');
            notesCell.className = 'notes-cell';
            const notesInput = document.createElement('textarea');
            notesInput.className = 'editable-field notes-input';
            notesInput.value = record.notes || '';
            notesInput.dataset.field = 'notes';
            notesInput.dataset.id = record.id;
            notesInput.style.textAlign = 'left';
            notesInput.addEventListener('blur', updateSdasinsField);
            notesInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    this.blur();
                }
            });
            notesCell.appendChild(notesInput);
            row.appendChild(notesCell);

            // Raw Notes (editable with textarea)
            const rawNotesCell = document.createElement('td');
            rawNotesCell.className = 'raw-notes-cell';
            const rawNotesInput = document.createElement('textarea');
            rawNotesInput.className = 'editable-field raw-notes-input';
            rawNotesInput.value = record.raw_notes || '';
            rawNotesInput.dataset.field = 'raw_notes';
            rawNotesInput.dataset.id = record.id;
            rawNotesInput.style.textAlign = 'left';
            rawNotesInput.addEventListener('blur', updateSdasinsField);
            rawNotesInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    this.blur();
                }
            });
            rawNotesCell.appendChild(rawNotesInput);
            row.appendChild(rawNotesCell);

            // Parsed fields (all editable)
            const editableFields = ['parsed_brand', 'parsed_mold', 'parsed_plastic', 'parsed_stamp'];
            editableFields.forEach(field => {
                const cell = document.createElement('td');
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'editable-field compact-input';
                input.value = record[field] || '';
                input.dataset.field = field;
                input.dataset.id = record.id;
                input.style.textAlign = 'left';
                input.addEventListener('blur', updateSdasinsField);
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        this.blur();
                    }
                });
                cell.appendChild(input);
                row.appendChild(cell);
            });

            // Parsed weight fields (numeric)
            const weightFields = ['parsed_min_weight', 'parsed_max_weight'];
            weightFields.forEach(field => {
                const cell = document.createElement('td');
                cell.className = 'weight-cell';
                const input = document.createElement('input');
                input.type = 'number';
                input.step = '0.1';
                input.className = 'editable-field weight-input';
                input.value = record[field] || '';
                input.dataset.field = field;
                input.dataset.id = record.id;
                input.addEventListener('blur', updateSdasinsField);
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        this.blur();
                    }
                });
                cell.appendChild(input);
                row.appendChild(cell);
            });

            // Color (dropdown)
            const colorCell = document.createElement('td');
            colorCell.className = 'color-cell';
            const colorSelect = document.createElement('select');
            colorSelect.className = 'editable-field color-input';
            colorSelect.dataset.field = 'color_id';
            colorSelect.dataset.id = record.id;
            colorSelect.addEventListener('change', updateSdasinsField);

            // Add empty option
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = '';
            colorSelect.appendChild(emptyOption);

            // Add color options
            colorOptions.forEach(color => {
                const option = document.createElement('option');
                option.value = color.id;
                option.textContent = color.color;
                if (record.color_id == color.id) {
                    option.selected = true;
                }
                colorSelect.appendChild(option);
            });

            colorCell.appendChild(colorSelect);
            row.appendChild(colorCell);

            // 30-Day Average Rank (read-only)
            const rankCell = document.createElement('td');
            rankCell.className = 'rank-cell';
            rankCell.textContent = record.so_rank_30day_avg ? Number(record.so_rank_30day_avg).toLocaleString() : '';
            rankCell.style.textAlign = 'right';
            row.appendChild(rankCell);

            // Rank Date (read-only)
            const rankDateCell = document.createElement('td');
            rankDateCell.className = 'date-cell';
            if (record.so_rank_30day_avg_date) {
                const date = new Date(record.so_rank_30day_avg_date);
                rankDateCell.textContent = date.toLocaleDateString();
            } else {
                rankDateCell.textContent = '';
            }
            rankDateCell.style.textAlign = 'center';
            row.appendChild(rankDateCell);

            // FBM Uploaded At (read-only)
            const fbmUploadedCell = document.createElement('td');
            fbmUploadedCell.className = 'date-cell';
            if (record.fbm_uploaded_at) {
                const date = new Date(record.fbm_uploaded_at);
                fbmUploadedCell.textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            } else {
                fbmUploadedCell.textContent = '';
            }
            fbmUploadedCell.style.textAlign = 'center';
            row.appendChild(fbmUploadedCell);

            return row;
        }

        function updateSdasinsField(event) {
            const input = event.target;
            const id = input.dataset.id;
            const field = input.dataset.field;
            const value = input.value;

            // For color dropdown, convert empty string to null
            const finalValue = (field === 'color_id' && value === '') ? null : (value || null);

            // Call API to update the field
            fetch('/api/sdasins/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: parseInt(id),
                    field: field,
                    value: finalValue
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the record in current data
                    const record = currentSdasinsData.find(r => r.id == id);
                    if (record) {
                        record[field] = finalValue;

                        // Check if the updated record still matches current filters
                        if (shouldRecordBeFilteredOut(record)) {
                            // Remove the record from current data and refresh display with scroll preservation
                            currentSdasinsData = currentSdasinsData.filter(r => r.id != id);
                            applyCurrentSdasinsSearchWithScrollPreservation();
                            console.log(`Record ${id} was filtered out after edit`);
                        } else {
                            // Visual feedback for successful update
                            input.style.backgroundColor = '#d4edda';
                            setTimeout(() => {
                                input.style.backgroundColor = '';
                            }, 1000);
                        }
                    }
                } else {
                    console.error('Error updating SDASINS field:', data.error || 'Unknown error');
                    // Reset to original value
                    const record = currentSdasinsData.find(r => r.id == id);
                    if (record) {
                        input.value = record[field] || '';
                    }
                    // Visual feedback for error
                    input.style.backgroundColor = '#f8d7da';
                    setTimeout(() => {
                        input.style.backgroundColor = '';
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error updating SDASINS field:', error.message);
                // Reset to original value
                const record = currentSdasinsData.find(r => r.id == id);
                if (record) {
                    input.value = record[field] || '';
                }
            });
        }

        function shouldRecordBeFilteredOut(record) {
            // If parent ASIN filter is active, ignore all other filters - only check parent ASIN
            if (currentParentAsinFilter) {
                return record.parent_asin !== currentParentAsinFilter;
            }

            // Check if any of the current filters would exclude this record

            // Get current filter states and values
            const filterRankBelow = document.getElementById('filterRankBelow').checked;
            const filterRankValue = parseFloat(document.getElementById('filterRankValue').value) || 0;
            const filterIdAbove = document.getElementById('filterIdAbove').checked;
            const filterIdValue = parseInt(document.getElementById('filterIdValue').value) || 0;
            const filterNotesNotContain = document.getElementById('filterNotesNotContain').checked;
            const filterNotesValue = document.getElementById('filterNotesValue').value || '';
            const filterNotesNotNull = document.getElementById('filterNotesNotNull').checked;
            const filterColorIsNull = document.getElementById('filterColorIsNull').checked;

            // Check rank filter
            if (filterRankBelow) {
                const rank = parseFloat(record.so_rank_30day_avg);
                if (!rank || rank >= filterRankValue) {
                    return true; // Should be filtered out
                }
            }

            // Check ID filter
            if (filterIdAbove) {
                const id = parseInt(record.id);
                if (!id || id <= filterIdValue) {
                    return true; // Should be filtered out
                }
            }

            // Check notes not contain filter
            if (filterNotesNotContain && filterNotesValue) {
                const notes = record.notes || '';
                if (notes.includes(filterNotesValue)) {
                    return true; // Should be filtered out
                }
            }

            // Check notes not null filter
            if (filterNotesNotNull) {
                if (!record.notes || record.notes.trim() === '') {
                    return true; // Should be filtered out
                }
            }

            // Check color is null filter
            if (filterColorIsNull) {
                if (record.color_id !== null && record.color_id !== undefined) {
                    return true; // Should be filtered out
                }
            }

            return false; // Record should stay
        }

        function setupSdasinsSortingEventListeners() {
            document.querySelectorAll('#sdasinsTable .sortable').forEach(header => {
                header.addEventListener('click', function(event) {
                    const column = this.getAttribute('data-column');
                    const type = this.getAttribute('data-type');

                    // Check if Ctrl key is held for cascading sort
                    const isCascading = event.ctrlKey || event.metaKey;

                    if (isCascading) {
                        // Cascading sort: add to sort criteria
                        const existingIndex = sdasinsSortCriteria.findIndex(criteria => criteria.column === column);

                        if (existingIndex >= 0) {
                            // Toggle direction for existing column
                            sdasinsSortCriteria[existingIndex].direction =
                                sdasinsSortCriteria[existingIndex].direction === 'asc' ? 'desc' : 'asc';
                        } else {
                            // Add new sort criteria
                            sdasinsSortCriteria.push({ column: column, direction: 'asc', type: type });
                        }
                    } else {
                        // Regular sort: replace all criteria
                        if (currentSdasinsSortColumn === column) {
                            currentSdasinsSortDirection = currentSdasinsSortDirection === 'asc' ? 'desc' : 'asc';
                        } else {
                            currentSdasinsSortColumn = column;
                            currentSdasinsSortDirection = 'asc';
                        }

                        // Reset cascading criteria to just this column
                        sdasinsSortCriteria = [{ column: column, direction: currentSdasinsSortDirection, type: type }];
                    }

                    sortAndDisplaySdasinsData();
                    updateSdasinsSortIndicators();
                });
            });
        }

        function sortAndDisplaySdasinsData() {
            // Show loading message for large datasets
            if (currentSdasinsData.length > 1000) {
                const tableBody = document.getElementById('sdasinsTableBody');
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Sorting ' + currentSdasinsData.length.toLocaleString() + ' records...</td></tr>';
            }

            // Use setTimeout to prevent UI blocking during sort
            setTimeout(() => {
                const sortedData = [...currentSdasinsData].sort((a, b) => {
                    // Apply cascading sort criteria
                    for (let criteria of sdasinsSortCriteria) {
                        let aVal = a[criteria.column];
                        let bVal = b[criteria.column];

                        // Handle null/undefined values
                        if (aVal === null || aVal === undefined) aVal = '';
                        if (bVal === null || bVal === undefined) bVal = '';

                        // Convert numbers for comparison
                        if (criteria.column === 'id' || criteria.column.includes('weight') || criteria.column === 'color_id' || criteria.column === 'so_rank_30day_avg') {
                            aVal = parseFloat(aVal) || 0;
                            bVal = parseFloat(bVal) || 0;
                        }

                        // Convert dates for comparison
                        if (criteria.column === 'so_rank_30day_avg_date' || criteria.column === 'fbm_uploaded_at') {
                            aVal = aVal ? new Date(aVal).getTime() : 0;
                            bVal = bVal ? new Date(bVal).getTime() : 0;
                        }

                        // Compare values
                        if (aVal < bVal) {
                            return criteria.direction === 'asc' ? -1 : 1;
                        }
                        if (aVal > bVal) {
                            return criteria.direction === 'asc' ? 1 : -1;
                        }
                        // If values are equal, continue to next sort criteria
                    }
                    return 0; // All criteria resulted in equal values
                });

                displaySdasinsData(sortedData);
            }, 10);
        }

        function updateSdasinsSortIndicators() {
            // Remove all sort classes from SDASINS table
            document.querySelectorAll('#sdasinsTable .sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
                header.style.position = 'relative';
                // Remove any existing sort order indicators
                const existingIndicator = header.querySelector('.sort-order');
                if (existingIndicator) {
                    existingIndicator.remove();
                }
            });

            // Add sort indicators for all active sort criteria
            sdasinsSortCriteria.forEach((criteria, index) => {
                const header = document.querySelector(`#sdasinsTable [data-column="${criteria.column}"]`);
                if (header) {
                    header.classList.add(criteria.direction === 'asc' ? 'sort-asc' : 'sort-desc');

                    // Add sort order number for cascading sorts
                    if (sdasinsSortCriteria.length > 1) {
                        const orderIndicator = document.createElement('span');
                        orderIndicator.className = 'sort-order';
                        orderIndicator.textContent = (index + 1).toString();
                        orderIndicator.style.cssText = `
                            position: absolute;
                            top: 2px;
                            right: 2px;
                            background: #007bff;
                            color: white;
                            border-radius: 50%;
                            width: 16px;
                            height: 16px;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                        `;
                        header.appendChild(orderIndicator);
                    }
                }
            });
        }

        function applyCurrentSdasinsSearch() {
            const searchInput = document.getElementById('sdasinsSearchInput');
            const searchTerm = searchInput.value.toLowerCase().trim();

            // If parent ASIN filter is active, show ALL records with that parent ASIN (ignore all other filters)
            if (currentParentAsinFilter) {
                console.log(`Filtering by parent ASIN: ${currentParentAsinFilter}`);
                console.log(`Total records to filter: ${currentSdasinsData.length}`);

                let filteredData = currentSdasinsData.filter(record => {
                    const matches = record.parent_asin === currentParentAsinFilter;
                    if (matches) {
                        console.log(`Match found: ID ${record.id}, Parent ASIN: ${record.parent_asin}`);
                    }
                    return matches;
                });

                console.log(`Found ${filteredData.length} records with parent ASIN: ${currentParentAsinFilter}`);

                // Apply search within parent ASIN results if search term exists
                if (searchTerm) {
                    const searchWords = searchTerm.split(/\s+/);
                    filteredData = filteredData.filter(record => {
                        const searchableText = [
                            record.id?.toString() || '',
                            record.parent_asin || '',
                            record.asin || '',
                            record.notes || '',
                            record.raw_notes || '',
                            record.parsed_brand || '',
                            record.parsed_mold || '',
                            record.parsed_plastic || '',
                            record.parsed_stamp || '',
                            record.parsed_min_weight?.toString() || '',
                            record.parsed_max_weight?.toString() || '',
                            record.color_id?.toString() || '',
                            record.color_name || '',
                            record.so_rank_30day_avg?.toString() || '',
                            record.so_rank_30day_avg_date || '',
                            record.fbm_uploaded_at || ''
                        ].join(' ').toLowerCase();

                        return searchWords.every(word => searchableText.includes(word));
                    });
                }

                // Sort the filtered data by ASIN
                const sortedFilteredData = [...filteredData].sort((a, b) => {
                    const aVal = a.asin || '';
                    const bVal = b.asin || '';
                    return aVal.localeCompare(bVal);
                });

                displaySdasinsData(sortedFilteredData);
                updateParentAsinFilterIndicator();
                return;
            }

            if (!searchTerm) {
                // If search is empty and no parent filter, show all current data sorted
                sortAndDisplaySdasinsData();
                return;
            }

            // Show loading message for large datasets
            if (currentSdasinsData.length > 1000) {
                const tableBody = document.getElementById('sdasinsTableBody');
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">Searching ' + currentSdasinsData.length.toLocaleString() + ' records...</td></tr>';
            }

            // Use setTimeout to prevent UI blocking during search
            setTimeout(() => {
                // Split search term into individual words
                const searchWords = searchTerm.split(/\s+/);

                let searchFilteredData = currentSdasinsData.filter(record => {
                    const searchableText = [
                        record.id?.toString() || '',
                        record.parent_asin || '',
                        record.asin || '',
                        record.notes || '',
                        record.raw_notes || '',
                        record.parsed_brand || '',
                        record.parsed_mold || '',
                        record.parsed_plastic || '',
                        record.parsed_stamp || '',
                        record.parsed_min_weight?.toString() || '',
                        record.parsed_max_weight?.toString() || '',
                        record.color_id?.toString() || '',
                        record.color_name || '',
                        record.so_rank_30day_avg?.toString() || '',
                        record.so_rank_30day_avg_date || '',
                        record.fbm_uploaded_at || ''
                    ].join(' ').toLowerCase();

                    // Check if ALL search words are found in the searchable text
                    return searchWords.every(word => searchableText.includes(word));
                });

                // Apply parent ASIN filter if active
                if (currentParentAsinFilter) {
                    searchFilteredData = searchFilteredData.filter(record =>
                        record.parent_asin === currentParentAsinFilter
                    );
                }

                // Sort the filtered data before displaying using cascading criteria
                const sortedFilteredData = [...searchFilteredData].sort((a, b) => {
                    // Apply cascading sort criteria
                    for (let criteria of sdasinsSortCriteria) {
                        let aVal = a[criteria.column];
                        let bVal = b[criteria.column];

                        // Handle null/undefined values
                        if (aVal === null || aVal === undefined) aVal = '';
                        if (bVal === null || bVal === undefined) bVal = '';

                        // Convert numbers for comparison
                        if (criteria.column === 'id' || criteria.column.includes('weight') || criteria.column === 'color_id' || criteria.column === 'so_rank_30day_avg') {
                            aVal = parseFloat(aVal) || 0;
                            bVal = parseFloat(bVal) || 0;
                        }

                        // Convert dates for comparison
                        if (criteria.column === 'so_rank_30day_avg_date' || criteria.column === 'fbm_uploaded_at') {
                            aVal = aVal ? new Date(aVal).getTime() : 0;
                            bVal = bVal ? new Date(bVal).getTime() : 0;
                        }

                        // Compare values
                        if (aVal < bVal) {
                            return criteria.direction === 'asc' ? -1 : 1;
                        }
                        if (aVal > bVal) {
                            return criteria.direction === 'asc' ? 1 : -1;
                        }
                        // If values are equal, continue to next sort criteria
                    }
                    return 0; // All criteria resulted in equal values
                });

                displaySdasinsData(sortedFilteredData);
            }, 10);
        }

        function applyCurrentSdasinsSearchWithScrollPreservation() {
            const searchInput = document.getElementById('sdasinsSearchInput');
            const searchTerm = searchInput.value.toLowerCase().trim();

            // If parent ASIN filter is active, show ALL records with that parent ASIN (ignore all other filters)
            if (currentParentAsinFilter) {
                const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
                console.log(`Scroll preservation: Filtering by parent ASIN: ${currentParentAsinFilter}`);

                let filteredData = currentSdasinsData.filter(record =>
                    record.parent_asin === currentParentAsinFilter
                );

                console.log(`Scroll preservation: Found ${filteredData.length} records with parent ASIN: ${currentParentAsinFilter}`);

                // Apply search within parent ASIN results if search term exists
                if (searchTerm) {
                    const searchWords = searchTerm.split(/\s+/);
                    filteredData = filteredData.filter(record => {
                        const searchableText = [
                            record.id?.toString() || '',
                            record.parent_asin || '',
                            record.asin || '',
                            record.notes || '',
                            record.raw_notes || '',
                            record.parsed_brand || '',
                            record.parsed_mold || '',
                            record.parsed_plastic || '',
                            record.parsed_stamp || '',
                            record.parsed_min_weight?.toString() || '',
                            record.parsed_max_weight?.toString() || '',
                            record.color_id?.toString() || '',
                            record.color_name || '',
                            record.so_rank_30day_avg?.toString() || '',
                            record.so_rank_30day_avg_date || '',
                            record.fbm_uploaded_at || ''
                        ].join(' ').toLowerCase();

                        return searchWords.every(word => searchableText.includes(word));
                    });
                }

                // Sort the filtered data by ASIN
                const sortedFilteredData = [...filteredData].sort((a, b) => {
                    const aVal = a.asin || '';
                    const bVal = b.asin || '';
                    return aVal.localeCompare(bVal);
                });

                displaySdasinsDataWithScrollPreservation(sortedFilteredData, scrollPosition);
                updateParentAsinFilterIndicator();
                return;
            }

            if (!searchTerm) {
                // If search is empty and no parent filter, show all current data sorted with scroll preservation
                sortAndDisplaySdasinsDataWithScrollPreservation();
                return;
            }

            // Save scroll position
            const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            // Use setTimeout to prevent UI blocking during search
            setTimeout(() => {
                // Split search term into individual words
                const searchWords = searchTerm.split(/\s+/);

                let searchFilteredData = currentSdasinsData.filter(record => {
                    const searchableText = [
                        record.id?.toString() || '',
                        record.parent_asin || '',
                        record.asin || '',
                        record.notes || '',
                        record.raw_notes || '',
                        record.parsed_brand || '',
                        record.parsed_mold || '',
                        record.parsed_plastic || '',
                        record.parsed_stamp || '',
                        record.parsed_min_weight?.toString() || '',
                        record.parsed_max_weight?.toString() || '',
                        record.color_id?.toString() || '',
                        record.color_name || '',
                        record.so_rank_30day_avg?.toString() || '',
                        record.so_rank_30day_avg_date || '',
                        record.fbm_uploaded_at || ''
                    ].join(' ').toLowerCase();

                    // Check if ALL search words are found in the searchable text
                    return searchWords.every(word => searchableText.includes(word));
                });

                // Apply parent ASIN filter if active
                if (currentParentAsinFilter) {
                    searchFilteredData = searchFilteredData.filter(record =>
                        record.parent_asin === currentParentAsinFilter
                    );
                }

                // Sort the filtered data before displaying using cascading criteria
                const sortedFilteredData = [...searchFilteredData].sort((a, b) => {
                    // Apply cascading sort criteria
                    for (let criteria of sdasinsSortCriteria) {
                        let aVal = a[criteria.column];
                        let bVal = b[criteria.column];

                        // Handle null/undefined values
                        if (aVal === null || aVal === undefined) aVal = '';
                        if (bVal === null || bVal === undefined) bVal = '';

                        // Convert numbers for comparison
                        if (criteria.column === 'id' || criteria.column.includes('weight') || criteria.column === 'color_id' || criteria.column === 'so_rank_30day_avg') {
                            aVal = parseFloat(aVal) || 0;
                            bVal = parseFloat(bVal) || 0;
                        }

                        // Convert dates for comparison
                        if (criteria.column === 'so_rank_30day_avg_date' || criteria.column === 'fbm_uploaded_at') {
                            aVal = aVal ? new Date(aVal).getTime() : 0;
                            bVal = bVal ? new Date(bVal).getTime() : 0;
                        }

                        // Compare values
                        if (aVal < bVal) {
                            return criteria.direction === 'asc' ? -1 : 1;
                        }
                        if (aVal > bVal) {
                            return criteria.direction === 'asc' ? 1 : -1;
                        }
                        // If values are equal, continue to next sort criteria
                    }
                    return 0; // All criteria resulted in equal values
                });

                // Display with scroll preservation
                displaySdasinsDataWithScrollPreservation(sortedFilteredData, scrollPosition);
            }, 10);
        }

        function sortAndDisplaySdasinsDataWithScrollPreservation() {
            // Save scroll position
            const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

            // Use setTimeout to prevent UI blocking during sort
            setTimeout(() => {
                const sortedData = [...currentSdasinsData].sort((a, b) => {
                    // Apply cascading sort criteria
                    for (let criteria of sdasinsSortCriteria) {
                        let aVal = a[criteria.column];
                        let bVal = b[criteria.column];

                        // Handle null/undefined values
                        if (aVal === null || aVal === undefined) aVal = '';
                        if (bVal === null || bVal === undefined) bVal = '';

                        // Convert numbers for comparison
                        if (criteria.column === 'id' || criteria.column.includes('weight') || criteria.column === 'color_id' || criteria.column === 'so_rank_30day_avg') {
                            aVal = parseFloat(aVal) || 0;
                            bVal = parseFloat(bVal) || 0;
                        }

                        // Convert dates for comparison
                        if (criteria.column === 'so_rank_30day_avg_date' || criteria.column === 'fbm_uploaded_at') {
                            aVal = aVal ? new Date(aVal).getTime() : 0;
                            bVal = bVal ? new Date(bVal).getTime() : 0;
                        }

                        // Compare values
                        if (aVal < bVal) {
                            return criteria.direction === 'asc' ? -1 : 1;
                        }
                        if (aVal > bVal) {
                            return criteria.direction === 'asc' ? 1 : -1;
                        }
                        // If values are equal, continue to next sort criteria
                    }
                    return 0; // All criteria resulted in equal values
                });

                displaySdasinsDataWithScrollPreservation(sortedData, scrollPosition);
            }, 10);
        }

        function displaySdasinsDataWithScrollPreservation(records, scrollPosition) {
            const tableBody = document.getElementById('sdasinsTableBody');
            const recordCountDiv = document.getElementById('sdasinsRecordCount');

            // Update record count display
            if (records && records.length > 0) {
                if (currentParentAsinFilter) {
                    recordCountDiv.textContent = `Showing ALL ${records.length.toLocaleString()} records with parent ASIN: ${currentParentAsinFilter}`;
                } else {
                    const totalRecords = currentSdasinsData ? currentSdasinsData.length : 0;
                    recordCountDiv.textContent = `Showing ${records.length.toLocaleString()} of ${totalRecords.toLocaleString()} records`;
                }
            } else {
                recordCountDiv.textContent = '';
            }

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="15" class="loading-message">No SDASINS records found.</td></tr>';
                return;
            }

            // Use setTimeout to prevent blocking the UI
            setTimeout(() => {
                renderRecordsInBatchesWithScrollPreservation(records, tableBody, scrollPosition);
            }, 10);
        }

        function renderRecordsInBatchesWithScrollPreservation(records, tableBody, scrollPosition) {
            tableBody.innerHTML = '';

            // Render in batches to prevent UI blocking
            const batchSize = 100;
            let currentIndex = 0;

            function renderBatch() {
                const endIndex = Math.min(currentIndex + batchSize, records.length);
                const fragment = document.createDocumentFragment();

                for (let i = currentIndex; i < endIndex; i++) {
                    const record = records[i];
                    const row = createRecordRow(record);
                    fragment.appendChild(row);
                }

                tableBody.appendChild(fragment);
                currentIndex = endIndex;

                if (currentIndex < records.length) {
                    // Continue with next batch
                    setTimeout(renderBatch, 10);
                } else {
                    // Restore scroll position after all batches are complete
                    setTimeout(() => {
                        window.scrollTo(0, scrollPosition);
                    }, 50);
                }
            }

            renderBatch();
        }

        function setupSdasinsSearchFilter() {
            const searchInput = document.getElementById('sdasinsSearchInput');
            searchInput.addEventListener('input', function() {
                applyCurrentSdasinsSearch();
            });
        }
    </script>
</body>
</html>
